/**
 * Token refresh endpoint for staff authentication
 * Refreshes JWT tokens using refresh token
 */

import { NextRequest } from 'next/server';
import { refreshToken } from '@/lib/auth';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { refreshToken: token } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['refreshToken']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    try {
      // Refresh the token
      const newTokens = refreshToken(token);

      if (!newTokens) {
        return createErrorResponse('Invalid or expired refresh token', 401);
      }

      return createResponse({
        token: newTokens.token,
        refreshToken: newTokens.refreshToken,
        expiresIn: newTokens.expiresIn
      }, true, 'Token refreshed successfully');

    } catch (tokenError) {
      console.error('Token refresh error:', tokenError);
      return createErrorResponse('Invalid or expired refresh token', 401);
    }

  } catch (error) {
    console.error('Staff token refresh error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
