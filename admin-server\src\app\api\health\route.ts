/**
 * Health check API endpoint
 */

import { NextRequest } from 'next/server';
import { createResponse } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      server: 'Admin Server',
      port: 3000,
      features: {
        authentication: 'configured',
        activityLogging: process.env.ENABLE_ACTIVITY_LOGGING === 'true' ? 'enabled' : 'disabled',
        database: 'pending_connection'
      }
    };

    return createResponse(
      healthData,
      true,
      'Admin server is running and healthy'
    );
  } catch (error) {
    console.error('Health check error:', error);
    return createResponse(
      { status: 'error', error: 'Health check failed' },
      false,
      'Health check failed',
      error instanceof Error ? error.message : 'Unknown error',
      500
    );
  }
}
