/**
 * Database connection test API endpoint
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    // Import database utilities
    const { testConnection } = await import('@/lib/db');
    
    // Test the connection
    const isConnected = await testConnection();
    
    if (isConnected) {
      return createResponse({
        status: 'connected',
        message: 'Database connection successful',
        timestamp: new Date().toISOString()
      }, true, 'Database is connected and working properly');
    } else {
      return createErrorResponse('Database connection failed', 500);
    }
  } catch (error) {
    console.error('Database test error:', error);
    return createErrorResponse(
      `Database connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
