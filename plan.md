# 🏗️ Innovative Centre Platform - Complete Architecture Plan

## 📋 Project Overview
- **Organization**: English tutoring with 4,000+ students and 40+ teachers
- **Architecture**: Microservices with RESTful APIs in monorepo structure
- **Technology Stack**: Next.js 15, PostgreSQL (Neon), TypeScript
- **Database**: PostgreSQL hosted on Neon (managed cloud service)
- **Development Approach**: Admin Server first, then Staff Server
- **Repository Structure**: Both services in single "Innovative Platform" folder
- **Activity Logging**: Comprehensive audit trail for all admin actions

---

## 📁 Monorepo Structure

```
Innovative Platform/
├── README.md
├── package.json (workspace configuration)
├── .gitignore
├── .env.example
├── plan.md
│
├── admin-server/
│   ├── src/
│   │   ├── app/
│   │   │   ├── api/
│   │   │   │   ├── auth/
│   │   │   │   ├── users/
│   │   │   │   ├── payments/
│   │   │   │   ├── cabinets/
│   │   │   │   ├── kpis/
│   │   │   │   └── activity-logs/
│   │   │   ├── dashboard/
│   │   │   ├── payments/
│   │   │   ├── users/
│   │   │   ├── cabinets/
│   │   │   ├── reports/
│   │   │   └── activity-logs/
│   │   ├── components/
│   │   │   ├── ui/
│   │   │   ├── forms/
│   │   │   ├── tables/
│   │   │   └── charts/
│   │   ├── lib/
│   │   │   ├── db.ts
│   │   │   ├── auth.ts
│   │   │   ├── activity-logger.ts
│   │   │   └── utils.ts
│   │   └── types/
│   ├── public/
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   └── tsconfig.json
│
├── staff-server/ (to be created after admin completion)
│   ├── src/
│   │   ├── app/
│   │   │   ├── api/
│   │   │   │   ├── auth/
│   │   │   │   ├── students/
│   │   │   │   ├── groups/
│   │   │   │   ├── leads/
│   │   │   │   ├── teachers/
│   │   │   │   └── activity-logs/
│   │   │   ├── dashboard/
│   │   │   ├── students/
│   │   │   ├── groups/
│   │   │   ├── leads/
│   │   │   ├── management/
│   │   │   └── activity-logs/
│   │   ├── components/
│   │   ├── lib/
│   │   │   ├── db.ts
│   │   │   ├── auth.ts
│   │   │   ├── activity-logger.ts
│   │   │   └── utils.ts
│   │   └── types/
│   ├── public/
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   └── tsconfig.json
│
├── shared/
│   ├── types/
│   │   ├── user.ts
│   │   ├── api.ts
│   │   ├── activity-log.ts
│   │   └── common.ts
│   ├── utils/
│   │   ├── validation.ts
│   │   ├── constants.ts
│   │   ├── activity-logger.ts
│   │   └── helpers.ts
│   └── database/
│       ├── admin-schema.sql
│       ├── staff-schema.sql
│       └── migrations/
│
└── docs/
    ├── api-documentation.md
    ├── deployment-guide.md
    ├── activity-logging-guide.md
    └── user-guides/
```

---

## 🗄️ Database Configuration (Neon PostgreSQL)

### Database Connection Strings:
- **Admin Database**: `postgresql://admin_owner:<EMAIL>/admin?sslmode=require`
- **Staff Database**: `postgresql://staff_owner:<EMAIL>/staff?sslmode=require`

### Admin Database Schema (`/shared/database/admin-schema.sql`):
```sql
-- User management for admin server
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'cashier', 'accountant')),
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Activity logging system
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- Create indexes for activity logs
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp);
CREATE INDEX idx_activity_logs_resource ON activity_logs(resource_type, resource_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);

-- Payment and financial management
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    payment_type VARCHAR(50) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'completed',
    processed_by UUID REFERENCES users(id),
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    paid_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Cabinet and resource management
CREATE TABLE cabinets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    capacity INTEGER NOT NULL,
    equipment TEXT[],
    hourly_rate DECIMAL(8,2),
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cabinet_bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cabinet_id UUID REFERENCES cabinets(id),
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    booked_by UUID REFERENCES users(id),
    purpose VARCHAR(255),
    status VARCHAR(50) DEFAULT 'confirmed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- KPI tracking
CREATE TABLE teacher_kpis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_id UUID,
    month DATE NOT NULL,
    students_taught INTEGER DEFAULT 0,
    retention_rate DECIMAL(5,2) DEFAULT 0,
    performance_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE reception_kpis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID,
    month DATE NOT NULL,
    leads_converted INTEGER DEFAULT 0,
    students_enrolled INTEGER DEFAULT 0,
    satisfaction_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔍 Activity Logging System

### Purpose:
- Track all administrative actions for audit and compliance
- Monitor user behavior and system changes
- Provide transparency for management oversight
- Enable rollback and change tracking

### Logged Activities:
- **User Management**: Create, update, delete, role changes
- **Payment Operations**: Payment recording, invoice generation, status changes
- **Cabinet Management**: Bookings, modifications, availability changes
- **KPI Updates**: Manual KPI adjustments and data imports
- **System Configuration**: Settings changes, user permissions

### Activity Log Structure:
```typescript
interface ActivityLog {
  id: string;
  userId: string;
  action: string; // 'CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT'
  resourceType: string; // 'USER', 'PAYMENT', 'CABINET', 'BOOKING'
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  description?: string;
}
```

### Activity Log API Endpoints:
- `GET /api/activity-logs` - List activity logs with filtering
- `GET /api/activity-logs/[id]` - Get specific log entry
- `GET /api/activity-logs/user/[userId]` - Get user-specific logs
- `GET /api/activity-logs/resource/[type]/[id]` - Get resource-specific logs

---

## 🏛️ System Architecture (Updated User Roles)

### Server 1: Admin Server
**Purpose**: Financial operations and administrative management
- **Users**:
  - **Admin**: System administration, user management, activity monitoring
  - **Cashier**: Payment processing, transaction recording
  - **Accountant**: Financial reporting, analysis, audit trail review
- **Core Functions**:
  - User management with activity logging
  - Payment recording with audit trail
  - Cabinet/classroom management
  - KPI analytics and reporting
  - Activity log monitoring and review
  - Financial reporting and accounting

### Server 2: Staff Server
**Purpose**: Operational management and day-to-day activities
- **Users**:
  - **Management**: Strategic oversight, performance monitoring
  - **Reception**: Student enrollment, lead management
  - **Teachers**: Class management, student progress
- **Core Functions**:
  - Student and group management
  - Lead management and conversion
  - Class scheduling and attendance
  - Student progress tracking
  - Operational reporting
  - Activity logging for operational changes

---

## 🔗 API Architecture (Admin Server)

### Authentication Routes (`/src/app/api/auth/`):
- `POST /api/auth/login` - User login (logged)
- `POST /api/auth/logout` - User logout (logged)
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token

### User Management (`/src/app/api/users/`):
- `GET /api/users` - List users
- `POST /api/users` - Create user (logged)
- `GET /api/users/[id]` - Get user details
- `PUT /api/users/[id]` - Update user (logged)
- `DELETE /api/users/[id]` - Deactivate user (logged)

### Payment Management (`/src/app/api/payments/`):
- `GET /api/payments` - List payments
- `POST /api/payments` - Record payment (logged)
- `GET /api/payments/[id]` - Get payment details
- `PUT /api/payments/[id]` - Update payment (logged)

### Cabinet Management (`/src/app/api/cabinets/`):
- `GET /api/cabinets` - List cabinets
- `POST /api/cabinets` - Create cabinet (logged)
- `GET /api/cabinets/[id]/bookings` - Get cabinet bookings
- `POST /api/cabinets/[id]/book` - Book cabinet (logged)
- `PUT /api/cabinets/[id]` - Update cabinet (logged)

### Activity Logs (`/src/app/api/activity-logs/`):
- `GET /api/activity-logs` - List activity logs (admin only)
- `GET /api/activity-logs/[id]` - Get specific log entry
- `GET /api/activity-logs/user/[userId]` - Get user-specific logs
- `GET /api/activity-logs/export` - Export logs for audit

---

## 🔧 Technology Configuration

### Admin Server Package.json (`/admin-server/package.json`):
```json
{
  "name": "innovative-centre-admin",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev -p 3000",
    "build": "next build",
    "start": "next start -p 3000",
    "lint": "next lint",
    "db:migrate": "node scripts/migrate.js"
  },
  "dependencies": {
    "next": "^15.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.4.0",
    "pg": "^8.11.0",
    "@types/pg": "^8.10.0",
    "bcryptjs": "^2.4.3",
    "@types/bcryptjs": "^2.4.0",
    "jsonwebtoken": "^9.0.0",
    "@types/jsonwebtoken": "^9.0.0",
    "zod": "^3.22.0",
    "react-hook-form": "^7.47.0",
    "@hookform/resolvers": "^3.3.0",
    "date-fns": "^2.30.0",
    "recharts": "^2.8.0"
  }
}
```

### Environment Variables (`.env.local` for admin-server):
```env
# Database
DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require

# Authentication
JWT_SECRET=your-jwt-secret-key-admin-server
NEXTAUTH_SECRET=your-nextauth-secret-admin

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365
```

---

## 🚀 Development Approach

### Phase 1: Admin Server Development (Weeks 1-4)

**Week 1: Foundation**
- Initialize Next.js 15 project in `/admin-server/`
- Set up Neon PostgreSQL connection
- Implement authentication system with activity logging
- Create basic project structure and activity logger utility

**Week 2: Core Features**
- User management (admin/cashier/accountant) with full activity logging
- Payment recording system with audit trail
- Basic dashboard layout with activity feed

**Week 3: Advanced Features**
- Cabinet management system with booking logs
- Invoice generation with change tracking
- Financial reporting with activity filters

**Week 4: Testing & Polish**
- KPI dashboard with activity monitoring
- Activity log viewer and export functionality
- Testing, bug fixes, and deployment to Vercel
- **User Testing Phase**

### Phase 2: Staff Server Development (After Admin Approval)
- Initialize staff server in `/staff-server/`
- Implement operational features with activity logging
- Inter-service communication for activity synchronization
- Final integration and testing

---

## 🎯 Key Features of Updated Plan

1. **Comprehensive Activity Logging**: Every admin action is tracked and auditable
2. **Database Integration**: Neon PostgreSQL with provided connection strings
3. **Monorepo Structure**: Both services organized in single repository
4. **Sequential Development**: Admin server first for testing and refinement
5. **Audit Trail**: Complete change history for compliance and transparency
6. **Role-Based Access**: Clear separation of admin, cashier, and accountant functions
7. **Activity Monitoring**: Real-time activity feeds and historical reporting

---

## 📋 Next Steps

1. **Initialize Admin Server**: Set up Next.js project in `/admin-server/`
2. **Database Setup**: Connect to Neon PostgreSQL and run schema
3. **Authentication & Logging**: Implement JWT auth with activity logging
4. **Core Features**: Build user management and payment systems
5. **Activity Dashboard**: Create activity log viewer and monitoring
6. **Testing**: Deploy and test admin server functionality
7. **Staff Server**: Begin development after admin approval

This comprehensive plan now includes robust activity logging for complete audit trails and transparency, using the provided Neon PostgreSQL connection strings, and maintains the monorepo structure for efficient development and deployment.
