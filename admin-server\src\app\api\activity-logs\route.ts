/**
 * Activity Logs API endpoint
 * Handles activity log retrieval with filtering and pagination
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { ActivityAction, ResourceType } from '@/types';

interface ActivityLogWithUser {
  id: string;
  user_id: string;
  action: ActivityAction;
  resource_type: ResourceType;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  description?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
}

// GET /api/activity-logs - List activity logs with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view activity logs)
    if (!hasPermission(authResult.user.role, 'activity-logs', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build query with filters
      let whereClause = 'WHERE 1=1';
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (filters.userId) {
        whereClause += ` AND al.user_id = $${paramIndex}`;
        queryParams.push(filters.userId);
        paramIndex++;
      }

      if (filters.action) {
        whereClause += ` AND al.action = $${paramIndex}`;
        queryParams.push(filters.action);
        paramIndex++;
      }

      if (filters.resourceType) {
        whereClause += ` AND al.resource_type = $${paramIndex}`;
        queryParams.push(filters.resourceType);
        paramIndex++;
      }

      if (filters.resourceId) {
        whereClause += ` AND al.resource_id = $${paramIndex}`;
        queryParams.push(filters.resourceId);
        paramIndex++;
      }

      if (filters.dateFrom) {
        whereClause += ` AND al.timestamp >= $${paramIndex}`;
        queryParams.push(filters.dateFrom);
        paramIndex++;
      }

      if (filters.dateTo) {
        whereClause += ` AND al.timestamp <= $${paramIndex}`;
        queryParams.push(filters.dateTo);
        paramIndex++;
      }

      if (filters.search) {
        whereClause += ` AND (al.description ILIKE $${paramIndex} OR u.name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`;
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total 
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ${whereClause}`,
        queryParams
      );
      const total = parseInt(countResult.rows[0].total);

      // Get activity logs with user information
      const offset = (pagination.page - 1) * pagination.limit;
      const logsResult = await query<ActivityLogWithUser>(
        `SELECT 
           al.id, al.user_id, al.action, al.resource_type, al.resource_id,
           al.old_values, al.new_values, al.ip_address, al.user_agent,
           al.timestamp, al.description,
           u.name as user_name, u.email as user_email, u.role as user_role
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ${whereClause} 
         ORDER BY al.timestamp DESC 
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...queryParams, pagination.limit, offset]
      );

      // Format the response
      const formattedLogs = logsResult.rows.map(log => ({
        id: log.id,
        userId: log.user_id,
        action: log.action,
        resourceType: log.resource_type,
        resourceId: log.resource_id,
        oldValues: log.old_values,
        newValues: log.new_values,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.timestamp,
        description: log.description,
        user: log.user_name ? {
          name: log.user_name,
          email: log.user_email,
          role: log.user_role
        } : null
      }));

      return createResponse({
        logs: formattedLogs,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        },
        filters: filters
      }, true, 'Activity logs retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting activity logs:', dbError);
      
      // For development, return mock data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockLogs = [
          {
            id: 'mock-log-1',
            userId: 'mock-admin-id',
            action: 'LOGIN' as ActivityAction,
            resourceType: 'USER' as ResourceType,
            resourceId: 'mock-admin-id',
            timestamp: new Date(),
            description: 'User logged in',
            user: {
              name: 'System Administrator',
              email: '<EMAIL>',
              role: 'admin'
            }
          },
          {
            id: 'mock-log-2',
            userId: 'mock-admin-id',
            action: 'CREATE' as ActivityAction,
            resourceType: 'USER' as ResourceType,
            resourceId: 'mock-user-id',
            timestamp: new Date(Date.now() - 3600000), // 1 hour ago
            description: 'Created new user account',
            user: {
              name: 'System Administrator',
              email: '<EMAIL>',
              role: 'admin'
            }
          }
        ];

        return createResponse({
          logs: mockLogs,
          pagination: {
            page: 1,
            limit: 20,
            total: mockLogs.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          },
          filters: filters
        }, true, 'Activity logs retrieved successfully (development mode)');
      }
      
      return createErrorResponse('Activity log service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get activity logs error:', error);
    return createErrorResponse('Failed to get activity logs', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
