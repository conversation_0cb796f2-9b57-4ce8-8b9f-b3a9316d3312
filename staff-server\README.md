# Innovative Centre Platform - Staff Server

This is the Staff Server component of the Innovative Centre Platform, built with Next.js 15 and TypeScript. It handles operational management including student enrollment, lead management, group/class management, and teacher operations.

## Features

- **Student Management**: Comprehensive student enrollment, tracking, and management
- **Lead Management**: Lead capture, assignment, and conversion tracking
- **Group/Class Management**: Class scheduling, teacher assignment, and student enrollment
- **User Management**: Management, Reception, and Teacher role management
- **Activity Logging**: Comprehensive audit trail for all operations
- **Reporting**: Operational analytics and KPI tracking
- **Role-Based Access Control**: Granular permissions for different user types

## User Roles

- **Management**: Strategic oversight, performance monitoring, full system access
- **Reception**: Student enrollment, lead management, customer service
- **Teachers**: Class management, student progress tracking, attendance

## Getting Started

### Prerequisites

- Node.js 18+ 
- PostgreSQL database (Neon)
- Admin Server running (for integration)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env.local
# Edit .env.local with your actual values
```

3. Run database migrations:
```bash
npm run db:migrate
```

4. Start the development server:
```bash
npm run dev
```

The server will start on [http://localhost:3001](http://localhost:3001).

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Type checking
npm run typecheck
```

## Project Structure

```
staff-server/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API routes
│   │   ├── dashboard/      # Dashboard pages
│   │   ├── students/       # Student management
│   │   ├── groups/         # Group management
│   │   ├── leads/          # Lead management
│   │   └── management/     # Management tools
│   ├── components/         # React components
│   ├── lib/               # Utilities and configurations
│   └── types/             # TypeScript type definitions
├── public/                # Static assets
├── tests/                 # Test files
└── scripts/              # Database and utility scripts
```

## API Documentation

The API documentation is available at `/api/docs` when running in development mode.

## Database

This server uses a separate PostgreSQL database for staff operations. The schema includes:

- Users (Management, Reception, Teachers)
- Students
- Leads
- Groups/Classes
- Activity Logs

## Integration

The Staff Server integrates with the Admin Server for:
- Financial data synchronization
- Cross-system activity logging
- Unified reporting

## Deployment

The application is configured for deployment on Vercel with automatic builds from the main branch.

## Contributing

1. Follow the existing code style and patterns
2. Write tests for new features
3. Update documentation as needed
4. Ensure all tests pass before submitting PRs

## License

Private - Innovative Centre Platform
