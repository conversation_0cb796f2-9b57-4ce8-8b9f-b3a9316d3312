/**
 * Activity logging utility for the Innovative Centre Platform
 */

import { 
  ActivityAction, 
  ResourceType, 
  ActivityContext,
  CreateActivityLogRequest,
  generateActivityDescription,
  sanitizeLogData
} from '../types/activity-log';

// Activity logger interface
export interface IActivityLogger {
  log(params: LogActivityParams): Promise<void>;
  logUserAction(params: LogUserActionParams): Promise<void>;
  logSystemAction(params: LogSystemActionParams): Promise<void>;
}

// Log activity parameters
export interface LogActivityParams {
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  context: ActivityContext;
}

// Log user action parameters
export interface LogUserActionParams {
  userId: string;
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Log system action parameters
export interface LogSystemActionParams {
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  description?: string;
  systemUserId?: string;
}

// Activity logger configuration
export interface ActivityLoggerConfig {
  enabled: boolean;
  sanitizeSensitiveData: boolean;
  maxLogSize: number;
  retentionDays: number;
  batchSize: number;
  flushInterval: number;
}

// Default configuration
const DEFAULT_CONFIG: ActivityLoggerConfig = {
  enabled: true,
  sanitizeSensitiveData: true,
  maxLogSize: 1000000, // 1MB
  retentionDays: 365,
  batchSize: 100,
  flushInterval: 5000 // 5 seconds
};

// Activity logger implementation
export class ActivityLogger implements IActivityLogger {
  private config: ActivityLoggerConfig;
  private logQueue: CreateActivityLogRequest[] = [];
  private flushTimer?: NodeJS.Timeout;

  constructor(
    private persistLog: (log: CreateActivityLogRequest) => Promise<void>,
    config?: Partial<ActivityLoggerConfig>
  ) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.startFlushTimer();
  }

  async log(params: LogActivityParams): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const logEntry: CreateActivityLogRequest = {
      userId: params.context.userId,
      action: params.action,
      resourceType: params.resourceType,
      resourceId: params.resourceId,
      oldValues: this.config.sanitizeSensitiveData 
        ? this.sanitizeData(params.oldValues) 
        : params.oldValues,
      newValues: this.config.sanitizeSensitiveData 
        ? this.sanitizeData(params.newValues) 
        : params.newValues,
      ipAddress: params.context.ipAddress,
      userAgent: params.context.userAgent,
      description: params.description || generateActivityDescription(
        params.action,
        params.resourceType
      )
    };

    await this.queueLog(logEntry);
  }

  async logUserAction(params: LogUserActionParams): Promise<void> {
    const context: ActivityContext = {
      userId: params.userId,
      ipAddress: params.ipAddress,
      userAgent: params.userAgent
    };

    await this.log({
      action: params.action,
      resourceType: params.resourceType,
      resourceId: params.resourceId,
      oldValues: params.oldValues,
      newValues: params.newValues,
      description: params.description,
      context
    });
  }

  async logSystemAction(params: LogSystemActionParams): Promise<void> {
    const context: ActivityContext = {
      userId: params.systemUserId || 'system'
    };

    await this.log({
      action: params.action,
      resourceType: params.resourceType,
      resourceId: params.resourceId,
      description: params.description || `System ${params.action.toLowerCase()} operation`,
      context
    });
  }

  private async queueLog(logEntry: CreateActivityLogRequest): Promise<void> {
    this.logQueue.push(logEntry);

    if (this.logQueue.length >= this.config.batchSize) {
      await this.flushLogs();
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logQueue.length === 0) {
      return;
    }

    const logsToFlush = [...this.logQueue];
    this.logQueue = [];

    try {
      await Promise.all(logsToFlush.map(log => this.persistLog(log)));
    } catch (error) {
      console.error('Failed to flush activity logs:', error);
      // Re-queue failed logs
      this.logQueue.unshift(...logsToFlush);
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushLogs().catch(error => {
        console.error('Failed to flush logs on timer:', error);
      });
    }, this.config.flushInterval);
  }

  private sanitizeData(data?: Record<string, any>): Record<string, any> | undefined {
    if (!data) {
      return data;
    }

    return sanitizeLogData(data);
  }

  // Cleanup method
  async cleanup(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    await this.flushLogs();
  }
}

// Helper functions for common logging scenarios
export function createUserActivityLogger(
  persistLog: (log: CreateActivityLogRequest) => Promise<void>,
  config?: Partial<ActivityLoggerConfig>
): ActivityLogger {
  return new ActivityLogger(persistLog, config);
}

// Pre-configured logging functions
export const LogActions = {
  // User management
  userCreated: (userId: string, newUser: any, context: ActivityContext) => ({
    action: ActivityAction.CREATE,
    resourceType: ResourceType.USER,
    resourceId: newUser.id,
    newValues: newUser,
    description: `Created user: ${newUser.email}`,
    context
  }),

  userUpdated: (userId: string, userUpdates: any, oldUser: any, context: ActivityContext) => ({
    action: ActivityAction.UPDATE,
    resourceType: ResourceType.USER,
    resourceId: userUpdates.id,
    oldValues: oldUser,
    newValues: userUpdates,
    description: `Updated user: ${oldUser.email}`,
    context
  }),

  userDeleted: (userId: string, deletedUser: any, context: ActivityContext) => ({
    action: ActivityAction.DELETE,
    resourceType: ResourceType.USER,
    resourceId: deletedUser.id,
    oldValues: deletedUser,
    description: `Deactivated user: ${deletedUser.email}`,
    context
  }),

  // Authentication
  userLogin: (userId: string, context: ActivityContext) => ({
    action: ActivityAction.LOGIN,
    resourceType: ResourceType.USER,
    resourceId: userId,
    description: 'User logged in',
    context
  }),

  userLogout: (userId: string, context: ActivityContext) => ({
    action: ActivityAction.LOGOUT,
    resourceType: ResourceType.USER,
    resourceId: userId,
    description: 'User logged out',
    context
  }),

  // Payment operations
  paymentCreated: (userId: string, payment: any, context: ActivityContext) => ({
    action: ActivityAction.CREATE,
    resourceType: ResourceType.PAYMENT,
    resourceId: payment.id,
    newValues: payment,
    description: `Recorded payment of ${payment.amount} for student ${payment.studentId}`,
    context
  }),

  paymentUpdated: (userId: string, paymentUpdates: any, oldPayment: any, context: ActivityContext) => ({
    action: ActivityAction.UPDATE,
    resourceType: ResourceType.PAYMENT,
    resourceId: paymentUpdates.id,
    oldValues: oldPayment,
    newValues: paymentUpdates,
    description: `Updated payment ${paymentUpdates.id}`,
    context
  })
};

// Export singleton instance
let globalActivityLogger: ActivityLogger | null = null;

export function initializeActivityLogger(
  persistLog: (log: CreateActivityLogRequest) => Promise<void>,
  config?: Partial<ActivityLoggerConfig>
): ActivityLogger {
  globalActivityLogger = new ActivityLogger(persistLog, config);
  return globalActivityLogger;
}

export function getActivityLogger(): ActivityLogger | null {
  return globalActivityLogger;
}
