/**
 * Invoices API endpoint
 * Handles invoice management operations (list, create)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logInvoiceOperation, getRequestContext } from '@/lib/activity-logger';
import { InvoiceStatus, ActivityAction } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface Invoice {
  id: string;
  student_id: string;
  amount: number;
  due_date: Date;
  paid_date?: Date;
  status: InvoiceStatus;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

interface InvoiceWithUser extends Invoice {
  created_by_name?: string;
  created_by_email?: string;
}

interface CreateInvoiceRequest {
  studentId: string;
  amount: number;
  dueDate: string;
}

// GET /api/invoices - List invoices with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'invoices', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    // Build query conditions
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Filter by student ID
    if (filters.studentId) {
      conditions.push(`i.student_id = $${paramIndex}`);
      params.push(filters.studentId);
      paramIndex++;
    }

    // Filter by status
    if (filters.status) {
      conditions.push(`i.status = $${paramIndex}`);
      params.push(filters.status);
      paramIndex++;
    }

    // Filter by due date range
    if (filters.dueDateFrom) {
      conditions.push(`i.due_date >= $${paramIndex}`);
      params.push(filters.dueDateFrom);
      paramIndex++;
    }

    if (filters.dueDateTo) {
      conditions.push(`i.due_date <= $${paramIndex}`);
      params.push(filters.dueDateTo);
      paramIndex++;
    }

    // Filter by amount range
    if (filters.amountFrom) {
      conditions.push(`i.amount >= $${paramIndex}`);
      params.push(parseFloat(filters.amountFrom));
      paramIndex++;
    }

    if (filters.amountTo) {
      conditions.push(`i.amount <= $${paramIndex}`);
      params.push(parseFloat(filters.amountTo));
      paramIndex++;
    }

    // Filter overdue invoices
    if (filters.overdue === 'true') {
      conditions.push(`i.due_date < CURRENT_DATE AND i.status = 'pending'`);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    let orderBy = 'i.due_date ASC';
    if (pagination.sortBy) {
      const sortColumn = pagination.sortBy === 'amount' ? 'i.amount' :
                        pagination.sortBy === 'dueDate' ? 'i.due_date' :
                        pagination.sortBy === 'status' ? 'i.status' :
                        'i.due_date';
      orderBy = `${sortColumn} ${pagination.sortOrder}`;
    }

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM invoices i
      ${whereClause}
    `;
    
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const dataSql = `
      SELECT 
        i.id,
        i.student_id,
        i.amount,
        i.due_date,
        i.paid_date,
        i.status,
        i.created_by,
        i.created_at,
        i.updated_at,
        u.name as created_by_name,
        u.email as created_by_email
      FROM invoices i
      LEFT JOIN users u ON i.created_by = u.id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(pagination.limit, offset);
    const dataResult = await query<InvoiceWithUser>(dataSql, params);

    // Format response
    const invoices = dataResult.rows.map(invoice => ({
      id: invoice.id,
      studentId: invoice.student_id,
      amount: invoice.amount,
      dueDate: invoice.due_date,
      paidDate: invoice.paid_date,
      status: invoice.status,
      createdBy: invoice.created_by,
      createdAt: invoice.created_at,
      updatedAt: invoice.updated_at,
      createdByUser: invoice.created_by_name ? {
        id: invoice.created_by,
        name: invoice.created_by_name,
        email: invoice.created_by_email
      } : undefined
    }));

    return createResponse({
      data: invoices,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
        hasNext: pagination.page < Math.ceil(total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    }, true, 'Invoices retrieved successfully');

  } catch (error) {
    console.error('Error fetching invoices:', error);
    return createErrorResponse('Failed to fetch invoices', 500);
  }
}

// POST /api/invoices - Create a new invoice
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'invoices', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body: CreateInvoiceRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, [
      'studentId', 'amount', 'dueDate'
    ]);
    
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { studentId, amount, dueDate } = body;

    // Validate amount
    if (amount < PAYMENT_CONFIG.MIN_AMOUNT || amount > PAYMENT_CONFIG.MAX_AMOUNT) {
      return createErrorResponse(
        `Amount must be between $${PAYMENT_CONFIG.MIN_AMOUNT} and $${PAYMENT_CONFIG.MAX_AMOUNT}`,
        400
      );
    }

    // Parse and validate due date
    const processedDueDate = new Date(dueDate);
    if (isNaN(processedDueDate.getTime())) {
      return createErrorResponse('Invalid due date format', 400);
    }

    // Check if due date is in the future
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (processedDueDate < today) {
      return createErrorResponse('Due date cannot be in the past', 400);
    }

    // Create invoice record
    const sql = `
      INSERT INTO invoices (
        student_id, amount, due_date, status, created_by
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING id, student_id, amount, due_date, paid_date, 
                status, created_by, created_at, updated_at
    `;

    const params = [
      studentId,
      amount,
      processedDueDate,
      InvoiceStatus.PENDING, // Default status
      authResult.user.id
    ];

    const result = await query<Invoice>(sql, params);
    const invoice = result.rows[0];

    // Log the invoice creation
    const context = getRequestContext(request.headers);
    await logInvoiceOperation(
      ActivityAction.CREATE,
      authResult.user.id,
      {
        id: invoice.id,
        studentId: invoice.student_id,
        amount: invoice.amount,
        dueDate: invoice.due_date,
        status: invoice.status
      },
      undefined,
      context
    );

    // Format response
    const responseInvoice = {
      id: invoice.id,
      studentId: invoice.student_id,
      amount: invoice.amount,
      dueDate: invoice.due_date,
      paidDate: invoice.paid_date,
      status: invoice.status,
      createdBy: invoice.created_by,
      createdAt: invoice.created_at,
      updatedAt: invoice.updated_at
    };

    return createResponse(
      responseInvoice,
      true,
      'Invoice created successfully',
      undefined,
      201
    );

  } catch (error) {
    console.error('Error creating invoice:', error);
    return createErrorResponse('Failed to create invoice', 500);
  }
}
