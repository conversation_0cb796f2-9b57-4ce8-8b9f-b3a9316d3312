/**
 * Cabinet Booking Creation API endpoint
 * Handles creating new bookings for specific cabinets
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logBookingOperation, getRequestContext } from '@/lib/activity-logger';
import { BookingStatus, ActivityAction } from '@/types';
import { BOOKING_CONFIG } from '@shared/utils/constants';

interface CabinetBooking {
  id: string;
  cabinet_id: string;
  date: Date;
  start_time: string;
  end_time: string;
  booked_by: string;
  purpose?: string;
  status: BookingStatus;
  created_at: Date;
  updated_at: Date;
}

interface CreateBookingRequest {
  date: string;
  startTime: string;
  endTime: string;
  purpose?: string;
}

// Helper function to validate time format (HH:MM)
function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

// Helper function to calculate duration in minutes
function calculateDurationMinutes(startTime: string, endTime: string): number {
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  return endMinutes - startMinutes;
}

// Helper function to check for booking conflicts
async function checkBookingConflicts(
  cabinetId: string, 
  date: string, 
  startTime: string, 
  endTime: string,
  excludeBookingId?: string
): Promise<boolean> {
  let sql = `
    SELECT id FROM cabinet_bookings 
    WHERE cabinet_id = $1 
      AND date = $2 
      AND status IN ('confirmed', 'pending')
      AND (
        (start_time <= $3 AND end_time > $3) OR
        (start_time < $4 AND end_time >= $4) OR
        (start_time >= $3 AND end_time <= $4)
      )
  `;
  
  const params = [cabinetId, date, startTime, endTime];
  
  if (excludeBookingId) {
    sql += ' AND id != $5';
    params.push(excludeBookingId);
  }
  
  const result = await query(sql, params);
  return result.rows.length > 0;
}

// POST /api/cabinets/[id]/book - Create a new booking for a cabinet
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id: cabinetId } = params;

    // Validate UUID format
    if (!isValidUUID(cabinetId)) {
      return createErrorResponse('Invalid cabinet ID format', 400);
    }

    // Check if cabinet exists and is available
    const cabinetCheck = await query(
      'SELECT id, name, is_available FROM cabinets WHERE id = $1',
      [cabinetId]
    );

    if (cabinetCheck.rows.length === 0) {
      return createErrorResponse('Cabinet not found', 404);
    }

    const cabinet = cabinetCheck.rows[0];
    if (!cabinet.is_available) {
      return createErrorResponse('Cabinet is not available for booking', 400);
    }

    const body: CreateBookingRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['date', 'startTime', 'endTime']);
    
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { date, startTime, endTime, purpose } = body;

    // Validate date format and ensure it's not in the past
    const bookingDate = new Date(date);
    if (isNaN(bookingDate.getTime())) {
      return createErrorResponse('Invalid date format', 400);
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (bookingDate < today) {
      return createErrorResponse('Cannot book cabinet for past dates', 400);
    }

    // Check advance booking limit
    const maxAdvanceDate = new Date();
    maxAdvanceDate.setDate(maxAdvanceDate.getDate() + BOOKING_CONFIG.ADVANCE_BOOKING_DAYS);
    
    if (bookingDate > maxAdvanceDate) {
      return createErrorResponse(
        `Cannot book more than ${BOOKING_CONFIG.ADVANCE_BOOKING_DAYS} days in advance`,
        400
      );
    }

    // Validate time formats
    if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) {
      return createErrorResponse('Invalid time format. Use HH:MM format', 400);
    }

    // Validate time logic
    if (startTime >= endTime) {
      return createErrorResponse('End time must be after start time', 400);
    }

    // Validate booking duration
    const durationMinutes = calculateDurationMinutes(startTime, endTime);
    
    if (durationMinutes < BOOKING_CONFIG.MIN_BOOKING_DURATION) {
      return createErrorResponse(
        `Minimum booking duration is ${BOOKING_CONFIG.MIN_BOOKING_DURATION} minutes`,
        400
      );
    }

    if (durationMinutes > BOOKING_CONFIG.MAX_BOOKING_DURATION) {
      return createErrorResponse(
        `Maximum booking duration is ${BOOKING_CONFIG.MAX_BOOKING_DURATION / 60} hours`,
        400
      );
    }

    // Check for booking conflicts
    const hasConflict = await checkBookingConflicts(cabinetId, date, startTime, endTime);
    
    if (hasConflict) {
      return createErrorResponse(
        'Time slot is already booked. Please choose a different time.',
        409
      );
    }

    // Create booking record
    const sql = `
      INSERT INTO cabinet_bookings (
        cabinet_id, date, start_time, end_time, booked_by, purpose, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, cabinet_id, date, start_time, end_time, booked_by, purpose, status, created_at, updated_at
    `;

    const bookingParams = [
      cabinetId,
      date,
      startTime,
      endTime,
      authResult.user.id,
      purpose || null,
      BookingStatus.CONFIRMED // Default status
    ];

    const result = await query<CabinetBooking>(sql, bookingParams);
    const booking = result.rows[0];

    // Log the booking creation
    const context = getRequestContext(request.headers);
    await logBookingOperation(
      ActivityAction.CREATE,
      authResult.user.id,
      {
        id: booking.id,
        cabinetId: booking.cabinet_id,
        date: booking.date,
        startTime: booking.start_time,
        endTime: booking.end_time,
        purpose: booking.purpose,
        status: booking.status
      },
      undefined,
      context
    );

    // Format response
    const responseBooking = {
      id: booking.id,
      cabinetId: booking.cabinet_id,
      date: booking.date,
      startTime: booking.start_time,
      endTime: booking.end_time,
      bookedBy: booking.booked_by,
      purpose: booking.purpose,
      status: booking.status,
      createdAt: booking.created_at,
      updatedAt: booking.updated_at,
      cabinet: {
        id: cabinet.id,
        name: cabinet.name
      },
      duration: `${Math.floor(durationMinutes / 60)}h ${durationMinutes % 60}m`
    };

    return createResponse(
      responseBooking,
      true,
      'Cabinet booked successfully',
      undefined,
      201
    );

  } catch (error) {
    console.error('Error creating booking:', error);
    return createErrorResponse('Failed to create booking', 500);
  }
}
