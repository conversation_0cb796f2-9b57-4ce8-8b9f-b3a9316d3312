/**
 * Payment Statistics API endpoint
 * Provides statistical information about payments and financial data
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface PaymentStats {
  totalRevenue: number;
  totalPayments: number;
  averagePayment: number;
  revenueByType: Record<string, number>;
  revenueByMethod: Record<string, number>;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
    paymentCount: number;
  }>;
  recentPayments: Array<{
    id: string;
    amount: number;
    paymentType: string;
    paymentMethod: string;
    paymentDate: Date;
    processedBy: string;
    processedByName: string;
  }>;
  statusBreakdown: Record<string, number>;
}

// GET /api/payments/stats - Get payment statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view payment stats)
    if (!hasPermission(authResult.user.role, 'reports', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build date filter
    const dateConditions: string[] = [];
    const dateParams: any[] = [];
    let paramIndex = 1;

    if (dateFrom) {
      dateConditions.push(`payment_date >= $${paramIndex}`);
      dateParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      dateConditions.push(`payment_date <= $${paramIndex}`);
      dateParams.push(dateTo + ' 23:59:59');
      paramIndex++;
    }

    const dateFilter = dateConditions.length > 0 ? `WHERE ${dateConditions.join(' AND ')}` : '';

    // Get total revenue and payment count
    const totalSql = `
      SELECT 
        COALESCE(SUM(amount), 0) as total_revenue,
        COUNT(*) as total_payments,
        COALESCE(AVG(amount), 0) as average_payment
      FROM payments 
      ${dateFilter} AND status = 'completed'
    `;

    const totalResult = await query(totalSql, dateParams);
    const totals = totalResult.rows[0];

    // Get revenue by payment type
    const typeSql = `
      SELECT 
        payment_type,
        SUM(amount) as revenue
      FROM payments 
      ${dateFilter} AND status = 'completed'
      GROUP BY payment_type
      ORDER BY revenue DESC
    `;

    const typeResult = await query(typeSql, dateParams);
    const revenueByType: Record<string, number> = {};
    typeResult.rows.forEach(row => {
      revenueByType[row.payment_type] = parseFloat(row.revenue);
    });

    // Get revenue by payment method
    const methodSql = `
      SELECT 
        payment_method,
        SUM(amount) as revenue
      FROM payments 
      ${dateFilter} AND status = 'completed'
      GROUP BY payment_method
      ORDER BY revenue DESC
    `;

    const methodResult = await query(methodSql, dateParams);
    const revenueByMethod: Record<string, number> = {};
    methodResult.rows.forEach(row => {
      revenueByMethod[row.payment_method] = parseFloat(row.revenue);
    });

    // Get monthly revenue (last 12 months or filtered period)
    const monthlySql = `
      SELECT 
        TO_CHAR(DATE_TRUNC('month', payment_date), 'YYYY-MM') as month,
        SUM(amount) as revenue,
        COUNT(*) as payment_count
      FROM payments 
      WHERE status = 'completed'
        ${dateFrom ? `AND payment_date >= $${dateParams.length > 0 ? 1 : 1}` : 'AND payment_date >= CURRENT_DATE - INTERVAL \'12 months\''}
        ${dateTo ? `AND payment_date <= $${dateParams.length > 1 ? 2 : dateParams.length + 1}` : ''}
      GROUP BY DATE_TRUNC('month', payment_date)
      ORDER BY month DESC
      LIMIT 12
    `;

    const monthlyResult = await query(monthlySql, dateParams);
    const monthlyRevenue = monthlyResult.rows.map(row => ({
      month: row.month,
      revenue: parseFloat(row.revenue),
      paymentCount: parseInt(row.payment_count)
    }));

    // Get recent payments
    const recentSql = `
      SELECT 
        p.id,
        p.amount,
        p.payment_type,
        p.payment_method,
        p.payment_date,
        p.processed_by,
        u.name as processed_by_name
      FROM payments p
      LEFT JOIN users u ON p.processed_by = u.id
      WHERE p.status = 'completed'
      ORDER BY p.payment_date DESC
      LIMIT 10
    `;

    const recentResult = await query(recentSql);
    const recentPayments = recentResult.rows.map(row => ({
      id: row.id,
      amount: parseFloat(row.amount),
      paymentType: row.payment_type,
      paymentMethod: row.payment_method,
      paymentDate: row.payment_date,
      processedBy: row.processed_by,
      processedByName: row.processed_by_name
    }));

    // Get status breakdown
    const statusSql = `
      SELECT 
        status,
        COUNT(*) as count
      FROM payments 
      ${dateFilter}
      GROUP BY status
      ORDER BY count DESC
    `;

    const statusResult = await query(statusSql, dateParams);
    const statusBreakdown: Record<string, number> = {};
    statusResult.rows.forEach(row => {
      statusBreakdown[row.status] = parseInt(row.count);
    });

    const stats: PaymentStats = {
      totalRevenue: parseFloat(totals.total_revenue),
      totalPayments: parseInt(totals.total_payments),
      averagePayment: parseFloat(totals.average_payment),
      revenueByType,
      revenueByMethod,
      monthlyRevenue,
      recentPayments,
      statusBreakdown
    };

    return createResponse(stats, true, 'Payment statistics retrieved successfully');

  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    return createErrorResponse('Failed to fetch payment statistics', 500);
  }
}
