/**
 * Authentication Logout API endpoint
 * Handles user logout and token invalidation
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest } from '@/lib/auth';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';

export async function POST(request: NextRequest) {
  try {
    // Get user from request
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    const user = authResult.user;
    const context = getRequestContext(request.headers);

    try {
      // Log logout event
      await logAuthEvent('LOGOUT', user.id, context);
    } catch (logError) {
      console.error('Failed to log logout event:', logError);
      // Continue with logout even if logging fails
    }

    // In a production system, you might want to:
    // 1. Add the token to a blacklist
    // 2. Clear any server-side sessions
    // 3. Update last logout time in database
    
    return createResponse(
      { message: 'Logged out successfully' },
      true,
      'Logout successful'
    );

  } catch (error) {
    console.error('Logout error:', error);
    return createErrorResponse(
      'Logout failed due to server error',
      500
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
