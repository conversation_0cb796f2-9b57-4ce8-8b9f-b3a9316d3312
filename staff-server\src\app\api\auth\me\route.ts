/**
 * Get current authenticated staff user endpoint
 * Returns user information for the authenticated user
 */

import { NextRequest } from 'next/server';
import { getUserFromRequest } from '@/lib/auth';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { UserRole } from '@/shared/types/common';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    try {
      // Get full user details from database
      const userResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [authResult.user.id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      // Check if user is still active
      if (!user.is_active) {
        return createErrorResponse('Account is deactivated', 401);
      }

      return createResponse({
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }, true, 'User information retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting staff user:', dbError);
      return createErrorResponse('Failed to retrieve user information', 500);
    }

  } catch (error) {
    console.error('Get staff user error:', error);
    return createErrorResponse('Failed to retrieve user information', 500);
  }
}
