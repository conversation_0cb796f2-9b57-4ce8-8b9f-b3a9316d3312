#!/bin/bash

# Test Script for Admin Server
# Comprehensive testing with different modes and coverage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Install test dependencies
install_dependencies() {
    print_status "Installing test dependencies..."
    
    npm install --save-dev \
        jest \
        @types/jest \
        jest-environment-jsdom \
        @testing-library/react \
        @testing-library/jest-dom \
        @testing-library/user-event \
        ts-jest \
        @next/eslint-plugin-next \
        eslint-plugin-testing-library \
        eslint-plugin-jest-dom
    
    print_success "Test dependencies installed"
}

# Run linting
run_lint() {
    print_status "Running ESLint..."
    
    if npm run lint; then
        print_success "Linting passed"
    else
        print_error "Linting failed"
        return 1
    fi
}

# Run type checking
run_typecheck() {
    print_status "Running TypeScript type checking..."
    
    if npx tsc --noEmit; then
        print_success "Type checking passed"
    else
        print_error "Type checking failed"
        return 1
    fi
}

# Run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    if npm run test:unit; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    if npm run test:integration; then
        print_success "Integration tests passed"
    else
        print_error "Integration tests failed"
        return 1
    fi
}

# Run all tests with coverage
run_tests_with_coverage() {
    print_status "Running all tests with coverage..."
    
    if npm run test:coverage; then
        print_success "Tests with coverage completed"
        print_status "Coverage report generated in coverage/ directory"
    else
        print_error "Tests with coverage failed"
        return 1
    fi
}

# Run tests in watch mode
run_tests_watch() {
    print_status "Running tests in watch mode..."
    print_warning "Press 'q' to quit watch mode"
    
    npm run test:watch
}

# Run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    if npm run test:performance; then
        print_success "Performance tests passed"
    else
        print_warning "Performance tests failed or not implemented"
    fi
}

# Generate test report
generate_report() {
    print_status "Generating test report..."
    
    # Create reports directory
    mkdir -p reports
    
    # Generate coverage report
    if [ -d "coverage" ]; then
        cp -r coverage reports/
        print_success "Coverage report copied to reports/coverage"
    fi
    
    # Generate test results summary
    cat > reports/test-summary.md << EOF
# Test Summary Report

Generated on: $(date)

## Test Results

### Unit Tests
- Status: $(npm run test:unit > /dev/null 2>&1 && echo "✅ PASSED" || echo "❌ FAILED")

### Integration Tests  
- Status: $(npm run test:integration > /dev/null 2>&1 && echo "✅ PASSED" || echo "❌ FAILED")

### Linting
- Status: $(npm run lint > /dev/null 2>&1 && echo "✅ PASSED" || echo "❌ FAILED")

### Type Checking
- Status: $(npx tsc --noEmit > /dev/null 2>&1 && echo "✅ PASSED" || echo "❌ FAILED")

## Coverage Information

See coverage/lcov-report/index.html for detailed coverage report.

## Next Steps

1. Review failed tests if any
2. Check coverage report for areas needing more tests
3. Update tests as needed for new features

EOF
    
    print_success "Test report generated in reports/ directory"
}

# Clean test artifacts
clean_test_artifacts() {
    print_status "Cleaning test artifacts..."
    
    rm -rf coverage
    rm -rf reports
    rm -rf .nyc_output
    rm -rf node_modules/.cache
    
    print_success "Test artifacts cleaned"
}

# Main function
main() {
    local mode=${1:-"all"}
    
    print_status "Starting test suite in '$mode' mode..."
    
    case $mode in
        "deps")
            check_dependencies
            install_dependencies
            ;;
        "lint")
            check_dependencies
            run_lint
            ;;
        "typecheck")
            check_dependencies
            run_typecheck
            ;;
        "unit")
            check_dependencies
            run_unit_tests
            ;;
        "integration")
            check_dependencies
            run_integration_tests
            ;;
        "coverage")
            check_dependencies
            run_tests_with_coverage
            ;;
        "watch")
            check_dependencies
            run_tests_watch
            ;;
        "performance")
            check_dependencies
            run_performance_tests
            ;;
        "report")
            check_dependencies
            generate_report
            ;;
        "clean")
            clean_test_artifacts
            ;;
        "ci")
            check_dependencies
            run_lint
            run_typecheck
            run_unit_tests
            run_integration_tests
            run_tests_with_coverage
            generate_report
            ;;
        "all")
            check_dependencies
            run_lint
            run_typecheck
            run_unit_tests
            run_integration_tests
            run_tests_with_coverage
            generate_report
            ;;
        *)
            print_error "Unknown mode: $mode"
            echo "Available modes:"
            echo "  deps        - Install test dependencies"
            echo "  lint        - Run linting"
            echo "  typecheck   - Run TypeScript type checking"
            echo "  unit        - Run unit tests"
            echo "  integration - Run integration tests"
            echo "  coverage    - Run tests with coverage"
            echo "  watch       - Run tests in watch mode"
            echo "  performance - Run performance tests"
            echo "  report      - Generate test report"
            echo "  clean       - Clean test artifacts"
            echo "  ci          - Run all tests for CI"
            echo "  all         - Run all tests (default)"
            exit 1
            ;;
    esac
    
    print_success "Test suite completed successfully!"
}

# Run main function with all arguments
main "$@"
