# Testing Framework Documentation

This document provides comprehensive information about the testing framework for the Admin Server application.

## Overview

The testing framework is built using Jest and React Testing Library, providing comprehensive coverage for:
- Unit tests for utilities, components, and API endpoints
- Integration tests for complete workflows
- Component testing with user interactions
- API endpoint testing with mocked dependencies

## Test Structure

```
admin-server/
├── src/
│   ├── lib/__tests__/           # Utility function tests
│   ├── components/__tests__/    # Component tests
│   └── app/api/**/__tests__/    # API endpoint tests
├── tests/
│   ├── utils/                   # Test utilities and helpers
│   ├── integration/             # Integration tests
│   └── fixtures/                # Test data fixtures
├── jest.config.js               # Jest configuration
├── jest.setup.js                # Global test setup
├── jest.env.js                  # Test environment variables
└── scripts/test.sh              # Test runner script
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- PostgreSQL (for integration tests)

### Installation

Install test dependencies:

```bash
npm install --save-dev jest @types/jest jest-environment-jsdom @testing-library/react @testing-library/jest-dom @testing-library/user-event ts-jest
```

Or use the test script:

```bash
chmod +x scripts/test.sh
./scripts/test.sh deps
```

## Running Tests

### Quick Commands

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run linting
npm run lint

# Run type checking
npm run typecheck
```

### Using Test Script

```bash
# Run all tests
./scripts/test.sh all

# Run specific test types
./scripts/test.sh unit
./scripts/test.sh integration
./scripts/test.sh coverage

# Run in watch mode
./scripts/test.sh watch

# CI mode (all checks)
./scripts/test.sh ci

# Clean test artifacts
./scripts/test.sh clean
```

## Test Categories

### 1. Unit Tests

Test individual functions, components, and modules in isolation.

**Location**: `src/**/__tests__/`

**Examples**:
- Utility functions (`src/lib/__tests__/utils.test.ts`)
- React components (`src/components/__tests__/PaymentForm.test.tsx`)
- API route handlers (`src/app/api/payments/__tests__/route.test.ts`)

**Best Practices**:
- Mock external dependencies
- Test edge cases and error conditions
- Aim for high code coverage (>80%)
- Use descriptive test names

### 2. Integration Tests

Test complete workflows and interactions between multiple components.

**Location**: `tests/integration/`

**Examples**:
- Payment CRUD operations
- User authentication flows
- Cabinet booking workflows

**Best Practices**:
- Test realistic user scenarios
- Use minimal mocking
- Test error handling and edge cases
- Verify data persistence

### 3. Component Tests

Test React components with user interactions and state changes.

**Features Tested**:
- Rendering with different props
- User interactions (clicks, form submissions)
- State changes and side effects
- Error handling and validation

**Best Practices**:
- Use `@testing-library/react` for rendering
- Test user behavior, not implementation details
- Use `userEvent` for realistic interactions
- Test accessibility features

## Test Utilities

### Test Helpers (`tests/utils/test-helpers.ts`)

Provides common utilities for testing:

```typescript
// Mock data generators
const user = mockUser({ role: 'admin' });
const payment = mockPayment({ amount: 150.00 });

// API response helpers
const response = mockApiResponse(data);
const error = mockErrorResponse('Error message');

// Form testing
await fillForm(container, { name: 'John', email: '<EMAIL>' });
await submitForm(container);

// Authentication helpers
mockAuthenticatedUser(user);
mockUnauthenticatedUser();
```

### Global Setup (`jest.setup.js`)

Provides global mocks and utilities:
- Next.js router mocking
- localStorage/sessionStorage mocking
- Fetch API mocking
- ResizeObserver/IntersectionObserver mocking

## Writing Tests

### Unit Test Example

```typescript
// src/lib/__tests__/utils.test.ts
import { validateEmail } from '../utils';

describe('validateEmail', () => {
  it('should validate correct email addresses', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('should reject invalid email addresses', () => {
    expect(validateEmail('invalid-email')).toBe(false);
    expect(validateEmail('@example.com')).toBe(false);
  });
});
```

### Component Test Example

```typescript
// src/components/__tests__/PaymentForm.test.tsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PaymentForm from '../PaymentForm';

describe('PaymentForm', () => {
  it('should submit form with valid data', async () => {
    const mockOnSubmit = jest.fn();
    const user = userEvent.setup();
    
    render(<PaymentForm onSubmit={mockOnSubmit} />);
    
    await user.type(screen.getByLabelText(/student id/i), 'STU001');
    await user.type(screen.getByLabelText(/amount/i), '150.00');
    await user.click(screen.getByRole('button', { name: /submit/i }));
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      studentId: 'STU001',
      amount: '150.00',
      // ... other fields
    });
  });
});
```

### API Test Example

```typescript
// src/app/api/payments/__tests__/route.test.ts
import { NextRequest } from 'next/server';
import { POST } from '../route';

describe('POST /api/payments', () => {
  it('should create payment for authenticated user', async () => {
    // Setup mocks
    mockGetUserFromRequest.mockResolvedValue({
      success: true,
      user: mockUser(),
    });
    
    const request = new NextRequest('http://localhost:3000/api/payments', {
      method: 'POST',
      body: JSON.stringify(validPaymentData),
    });
    
    const response = await POST(request);
    const data = await response.json();
    
    expect(response.status).toBe(201);
    expect(data.success).toBe(true);
  });
});
```

## Coverage Requirements

### Minimum Coverage Thresholds

- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Coverage Reports

Coverage reports are generated in multiple formats:
- **HTML**: `coverage/lcov-report/index.html`
- **LCOV**: `coverage/lcov.info`
- **Text**: Console output during test runs

### Improving Coverage

1. Identify uncovered code in coverage reports
2. Add tests for missing branches and functions
3. Test error conditions and edge cases
4. Remove dead code if coverage reveals unused code

## Continuous Integration

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: ./scripts/test.sh ci
      - uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### Pre-commit Hooks

```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "npm run test:related"
    ]
  }
}
```

## Debugging Tests

### Running Single Tests

```bash
# Run specific test file
npm test -- PaymentForm.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="should validate"

# Run tests in specific directory
npm test -- src/components
```

### Debug Mode

```bash
# Run with debug output
npm test -- --verbose

# Run with coverage for specific files
npm test -- --coverage --collectCoverageFrom="src/components/**"
```

### VS Code Integration

Add to `.vscode/launch.json`:

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Jest Tests",
  "program": "${workspaceFolder}/node_modules/.bin/jest",
  "args": ["--runInBand"],
  "console": "integratedTerminal",
  "internalConsoleOptions": "neverOpen"
}
```

## Best Practices

### General Guidelines

1. **Test Behavior, Not Implementation**
   - Focus on what the code does, not how it does it
   - Test user-facing functionality
   - Avoid testing internal state or private methods

2. **Write Descriptive Test Names**
   ```typescript
   // Good
   it('should return 400 when amount is negative')
   
   // Bad
   it('should fail')
   ```

3. **Use Arrange-Act-Assert Pattern**
   ```typescript
   it('should calculate total with tax', () => {
     // Arrange
     const amount = 100;
     const taxRate = 0.1;
     
     // Act
     const total = calculateTotal(amount, taxRate);
     
     // Assert
     expect(total).toBe(110);
   });
   ```

4. **Mock External Dependencies**
   - Mock API calls, database queries, and external services
   - Use dependency injection for easier testing
   - Keep mocks simple and focused

5. **Test Edge Cases**
   - Empty inputs, null values, undefined
   - Boundary conditions (min/max values)
   - Error conditions and exceptions

### Component Testing

1. **Test User Interactions**
   ```typescript
   await user.click(button);
   await user.type(input, 'text');
   await user.selectOptions(select, 'option');
   ```

2. **Test Accessibility**
   ```typescript
   expect(screen.getByRole('button', { name: /submit/i })).toBeInTheDocument();
   expect(screen.getByLabelText(/email/i)).toBeRequired();
   ```

3. **Test Error States**
   ```typescript
   expect(screen.getByText(/error message/i)).toBeInTheDocument();
   expect(screen.getByRole('alert')).toBeInTheDocument();
   ```

### API Testing

1. **Test All HTTP Methods**
   - GET, POST, PUT, DELETE
   - Test success and error responses
   - Test different status codes

2. **Test Authentication and Authorization**
   ```typescript
   // Test unauthenticated access
   mockGetUserFromRequest.mockResolvedValue({ success: false });
   
   // Test insufficient permissions
   mockHasPermission.mockReturnValue(false);
   ```

3. **Test Input Validation**
   ```typescript
   // Test missing required fields
   const invalidData = { /* missing required fields */ };
   
   // Test invalid data types
   const invalidData = { amount: 'not-a-number' };
   ```

## Troubleshooting

### Common Issues

1. **Tests Timeout**
   - Increase timeout in jest.config.js
   - Check for unresolved promises
   - Ensure async operations are properly awaited

2. **Mock Issues**
   - Clear mocks between tests: `jest.clearAllMocks()`
   - Reset modules: `jest.resetModules()`
   - Check mock implementation

3. **Environment Issues**
   - Verify environment variables in jest.env.js
   - Check Node.js version compatibility
   - Ensure test database is available

### Getting Help

1. Check Jest documentation: https://jestjs.io/docs/
2. React Testing Library docs: https://testing-library.com/docs/react-testing-library/intro/
3. Review existing tests for patterns
4. Ask team members for guidance

## Maintenance

### Regular Tasks

1. **Update Dependencies**
   ```bash
   npm update --save-dev jest @testing-library/react
   ```

2. **Review Coverage Reports**
   - Identify areas needing more tests
   - Remove obsolete tests
   - Update tests for changed functionality

3. **Performance Monitoring**
   - Monitor test execution time
   - Optimize slow tests
   - Parallelize test execution

### Test Cleanup

1. **Remove Obsolete Tests**
   - Delete tests for removed features
   - Update tests for changed APIs
   - Consolidate duplicate tests

2. **Refactor Test Code**
   - Extract common test utilities
   - Reduce code duplication
   - Improve test readability
