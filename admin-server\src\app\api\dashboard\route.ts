/**
 * Dashboard API endpoint
 * Aggregates statistics from all modules for the main dashboard
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface DashboardData {
  overview: {
    totalUsers: number;
    activeUsers: number;
    totalRevenue: number;
    totalPayments: number;
    pendingInvoices: number;
    activeBookings: number;
    totalCabinets: number;
    recentActivity: number;
  };
  financialSummary: {
    monthlyRevenue: number;
    monthlyGrowth: number;
    pendingAmount: number;
    completedPayments: number;
    revenueByType: Record<string, number>;
    revenueByMethod: Record<string, number>;
  };
  bookingSummary: {
    todayBookings: number;
    upcomingBookings: number;
    utilizationRate: number;
    popularTimeSlots: Array<{
      timeSlot: string;
      bookingCount: number;
    }>;
  };
  recentActivity: Array<{
    id: string;
    action: string;
    description: string;
    timestamp: Date;
    userId: string;
    userName: string;
  }>;
  trends: {
    dailyRevenue: Array<{
      date: string;
      revenue: number;
      payments: number;
    }>;
    weeklyBookings: Array<{
      date: string;
      bookings: number;
      confirmed: number;
    }>;
    userGrowth: Array<{
      date: string;
      newUsers: number;
      totalUsers: number;
    }>;
  };
}

// GET /api/dashboard - Get comprehensive dashboard data
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'dashboard', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get overview statistics
      const overviewSql = `
        WITH user_stats AS (
          SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = true THEN 1 END) as active_users
          FROM users
        ),
        payment_stats AS (
          SELECT 
            COUNT(*) as total_payments,
            COALESCE(SUM(amount), 0) as total_revenue
          FROM payments 
          WHERE status = 'completed'
        ),
        invoice_stats AS (
          SELECT 
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_invoices
          FROM invoices
        ),
        booking_stats AS (
          SELECT 
            COUNT(CASE WHEN date >= CURRENT_DATE AND status IN ('confirmed', 'pending') THEN 1 END) as active_bookings
          FROM cabinet_bookings
        ),
        cabinet_stats AS (
          SELECT COUNT(*) as total_cabinets FROM cabinets WHERE is_available = true
        ),
        activity_stats AS (
          SELECT COUNT(*) as recent_activity 
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        )
        SELECT 
          u.total_users, u.active_users,
          p.total_payments, p.total_revenue,
          i.pending_invoices,
          b.active_bookings,
          c.total_cabinets,
          a.recent_activity
        FROM user_stats u, payment_stats p, invoice_stats i, booking_stats b, cabinet_stats c, activity_stats a
      `;

      const overviewResult = await query(overviewSql);
      const overview = overviewResult.rows[0];

      // Get financial summary
      const financialSql = `
        WITH current_month AS (
          SELECT 
            COALESCE(SUM(amount), 0) as monthly_revenue,
            COUNT(*) as monthly_payments
          FROM payments 
          WHERE status = 'completed' 
            AND DATE_TRUNC('month', payment_date) = DATE_TRUNC('month', CURRENT_DATE)
        ),
        previous_month AS (
          SELECT COALESCE(SUM(amount), 0) as prev_revenue
          FROM payments 
          WHERE status = 'completed' 
            AND DATE_TRUNC('month', payment_date) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
        ),
        pending_amount AS (
          SELECT COALESCE(SUM(amount), 0) as pending
          FROM invoices 
          WHERE status = 'pending'
        ),
        revenue_by_type AS (
          SELECT 
            payment_type,
            SUM(amount) as revenue
          FROM payments 
          WHERE status = 'completed' 
            AND payment_date >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY payment_type
        ),
        revenue_by_method AS (
          SELECT 
            payment_method,
            SUM(amount) as revenue
          FROM payments 
          WHERE status = 'completed' 
            AND payment_date >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY payment_method
        )
        SELECT 
          cm.monthly_revenue,
          cm.monthly_payments,
          pm.prev_revenue,
          pa.pending,
          CASE 
            WHEN pm.prev_revenue > 0 THEN 
              ROUND(((cm.monthly_revenue - pm.prev_revenue) / pm.prev_revenue * 100)::numeric, 2)
            ELSE 0 
          END as growth_rate
        FROM current_month cm, previous_month pm, pending_amount pa
      `;

      const financialResult = await query(financialSql);
      const financial = financialResult.rows[0];

      // Get revenue breakdown
      const revenueByTypeSql = `
        SELECT payment_type, SUM(amount) as revenue
        FROM payments 
        WHERE status = 'completed' AND payment_date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY payment_type
      `;
      const revenueByTypeResult = await query(revenueByTypeSql);
      const revenueByType: Record<string, number> = {};
      revenueByTypeResult.rows.forEach(row => {
        revenueByType[row.payment_type] = parseFloat(row.revenue);
      });

      const revenueByMethodSql = `
        SELECT payment_method, SUM(amount) as revenue
        FROM payments 
        WHERE status = 'completed' AND payment_date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY payment_method
      `;
      const revenueByMethodResult = await query(revenueByMethodSql);
      const revenueByMethod: Record<string, number> = {};
      revenueByMethodResult.rows.forEach(row => {
        revenueByMethod[row.payment_method] = parseFloat(row.revenue);
      });

      // Get booking summary
      const bookingSql = `
        WITH today_bookings AS (
          SELECT COUNT(*) as today_count
          FROM cabinet_bookings 
          WHERE date = CURRENT_DATE AND status IN ('confirmed', 'pending')
        ),
        upcoming_bookings AS (
          SELECT COUNT(*) as upcoming_count
          FROM cabinet_bookings 
          WHERE date > CURRENT_DATE AND status IN ('confirmed', 'pending')
        ),
        utilization AS (
          SELECT 
            COALESCE(
              ROUND(
                (COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END)::DECIMAL / 
                 NULLIF(COUNT(c.id) * 10, 0)) * 100, 2
              ), 0
            ) as utilization_rate
          FROM cabinets c
          LEFT JOIN cabinet_bookings cb ON c.id = cb.cabinet_id 
            AND cb.date >= CURRENT_DATE - INTERVAL '7 days'
          WHERE c.is_available = true
        )
        SELECT tb.today_count, ub.upcoming_count, u.utilization_rate
        FROM today_bookings tb, upcoming_bookings ub, utilization u
      `;

      const bookingResult = await query(bookingSql);
      const booking = bookingResult.rows[0];

      // Get popular time slots
      const timeSlotsSql = `
        SELECT 
          CONCAT(
            LPAD(EXTRACT(HOUR FROM start_time)::text, 2, '0'), 
            ':00-', 
            LPAD((EXTRACT(HOUR FROM start_time) + 1)::text, 2, '0'), 
            ':00'
          ) as time_slot,
          COUNT(*) as booking_count
        FROM cabinet_bookings 
        WHERE status = 'confirmed' 
          AND date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY EXTRACT(HOUR FROM start_time)
        ORDER BY booking_count DESC
        LIMIT 5
      `;

      const timeSlotsResult = await query(timeSlotsSql);
      const popularTimeSlots = timeSlotsResult.rows.map(row => ({
        timeSlot: row.time_slot,
        bookingCount: parseInt(row.booking_count)
      }));

      // Get recent activity
      const activitySql = `
        SELECT 
          al.id,
          al.action,
          al.details->>'description' as description,
          al.timestamp,
          al.user_id,
          u.name as user_name
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ORDER BY al.timestamp DESC
        LIMIT 10
      `;

      const activityResult = await query(activitySql);
      const recentActivity = activityResult.rows.map(row => ({
        id: row.id,
        action: row.action,
        description: row.description || `${row.action} operation`,
        timestamp: row.timestamp,
        userId: row.user_id,
        userName: row.user_name || 'Unknown User'
      }));

      // Get trends data
      const dailyRevenueSql = `
        SELECT 
          DATE(payment_date) as date,
          SUM(amount) as revenue,
          COUNT(*) as payments
        FROM payments 
        WHERE status = 'completed' 
          AND payment_date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(payment_date)
        ORDER BY date DESC
        LIMIT 30
      `;

      const dailyRevenueResult = await query(dailyRevenueSql);
      const dailyRevenue = dailyRevenueResult.rows.map(row => ({
        date: row.date,
        revenue: parseFloat(row.revenue),
        payments: parseInt(row.payments)
      }));

      const weeklyBookingsSql = `
        SELECT 
          DATE(date) as date,
          COUNT(*) as bookings,
          COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed
        FROM cabinet_bookings 
        WHERE date >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(date)
        ORDER BY date DESC
        LIMIT 30
      `;

      const weeklyBookingsResult = await query(weeklyBookingsSql);
      const weeklyBookings = weeklyBookingsResult.rows.map(row => ({
        date: row.date,
        bookings: parseInt(row.bookings),
        confirmed: parseInt(row.confirmed)
      }));

      const userGrowthSql = `
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as new_users,
          SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as total_users
        FROM users 
        WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `;

      const userGrowthResult = await query(userGrowthSql);
      const userGrowth = userGrowthResult.rows.map(row => ({
        date: row.date,
        newUsers: parseInt(row.new_users),
        totalUsers: parseInt(row.total_users)
      }));

      const dashboardData: DashboardData = {
        overview: {
          totalUsers: parseInt(overview.total_users || 0),
          activeUsers: parseInt(overview.active_users || 0),
          totalRevenue: parseFloat(overview.total_revenue || 0),
          totalPayments: parseInt(overview.total_payments || 0),
          pendingInvoices: parseInt(overview.pending_invoices || 0),
          activeBookings: parseInt(overview.active_bookings || 0),
          totalCabinets: parseInt(overview.total_cabinets || 0),
          recentActivity: parseInt(overview.recent_activity || 0)
        },
        financialSummary: {
          monthlyRevenue: parseFloat(financial.monthly_revenue || 0),
          monthlyGrowth: parseFloat(financial.growth_rate || 0),
          pendingAmount: parseFloat(financial.pending || 0),
          completedPayments: parseInt(financial.monthly_payments || 0),
          revenueByType,
          revenueByMethod
        },
        bookingSummary: {
          todayBookings: parseInt(booking.today_count || 0),
          upcomingBookings: parseInt(booking.upcoming_count || 0),
          utilizationRate: parseFloat(booking.utilization_rate || 0),
          popularTimeSlots
        },
        recentActivity,
        trends: {
          dailyRevenue,
          weeklyBookings,
          userGrowth
        }
      };

      return createResponse(dashboardData, true, 'Dashboard data retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting dashboard data:', dbError);
      
      // For development, return mock dashboard data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockDashboardData: DashboardData = {
          overview: {
            totalUsers: 125,
            activeUsers: 118,
            totalRevenue: 45750.00,
            totalPayments: 342,
            pendingInvoices: 23,
            activeBookings: 18,
            totalCabinets: 8,
            recentActivity: 47
          },
          financialSummary: {
            monthlyRevenue: 12450.00,
            monthlyGrowth: 8.5,
            pendingAmount: 3200.00,
            completedPayments: 89,
            revenueByType: {
              'tuition': 8500.00,
              'registration': 2200.00,
              'materials': 1750.00
            },
            revenueByMethod: {
              'card': 7200.00,
              'cash': 3800.00,
              'bank_transfer': 1450.00
            }
          },
          bookingSummary: {
            todayBookings: 6,
            upcomingBookings: 24,
            utilizationRate: 72.5,
            popularTimeSlots: [
              { timeSlot: '14:00-15:00', bookingCount: 15 },
              { timeSlot: '15:00-16:00', bookingCount: 12 },
              { timeSlot: '16:00-17:00', bookingCount: 11 }
            ]
          },
          recentActivity: [
            {
              id: '1',
              action: 'PAYMENT_CREATE',
              description: 'Payment of $150 recorded for student STU001',
              timestamp: new Date(),
              userId: 'user-1',
              userName: 'Admin User'
            }
          ],
          trends: {
            dailyRevenue: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - i * ********).toISOString().split('T')[0],
              revenue: Math.random() * 1000 + 500,
              payments: Math.floor(Math.random() * 10) + 5
            })),
            weeklyBookings: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - i * ********).toISOString().split('T')[0],
              bookings: Math.floor(Math.random() * 8) + 2,
              confirmed: Math.floor(Math.random() * 6) + 1
            })),
            userGrowth: Array.from({ length: 7 }, (_, i) => ({
              date: new Date(Date.now() - i * ********).toISOString().split('T')[0],
              newUsers: Math.floor(Math.random() * 3) + 1,
              totalUsers: 125 - i
            }))
          }
        };

        return createResponse(mockDashboardData, true, 'Dashboard data retrieved successfully (development mode)');
      }
      
      return createErrorResponse('Dashboard service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get dashboard data error:', error);
    return createErrorResponse('Failed to get dashboard data', 500);
  }
}
