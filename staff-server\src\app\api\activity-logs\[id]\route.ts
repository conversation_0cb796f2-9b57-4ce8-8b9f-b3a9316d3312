/**
 * Individual activity log endpoint
 * Get specific activity log entry with full details
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  isValidUUID
} from '@/lib/utils';
import { UserRole } from '@/shared/types/common';

interface ActivityLogWithUser {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  description?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
}

// GET /api/activity-logs/[id] - Get specific activity log
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid activity log ID format', 400);
    }

    // Check permissions - only management can view activity logs
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get activity log details with user information
      const logResult = await query<ActivityLogWithUser>(
        `SELECT 
           al.id, al.user_id, al.action, al.resource_type, al.resource_id,
           al.old_values, al.new_values, al.ip_address, al.user_agent,
           al.timestamp, al.description,
           u.name as user_name, u.email as user_email, u.role as user_role
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         WHERE al.id = $1`,
        [id]
      );

      if (logResult.rows.length === 0) {
        return createErrorResponse('Activity log not found', 404);
      }

      const log = logResult.rows[0];

      return createResponse({
        id: log.id,
        userId: log.user_id,
        action: log.action,
        resourceType: log.resource_type,
        resourceId: log.resource_id,
        oldValues: log.old_values,
        newValues: log.new_values,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.timestamp,
        description: log.description,
        user: {
          name: log.user_name,
          email: log.user_email,
          role: log.user_role
        }
      }, true, 'Activity log retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving activity log:', dbError);
      return createErrorResponse('Failed to retrieve activity log', 500);
    }

  } catch (error) {
    console.error('Get activity log error:', error);
    return createErrorResponse('Failed to retrieve activity log', 500);
  }
}
