/**
 * Cabinet Statistics API endpoint
 * Provides statistical information about cabinets and bookings
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface CabinetStats {
  totalCabinets: number;
  availableCabinets: number;
  unavailableCabinets: number;
  totalBookings: number;
  confirmedBookings: number;
  pendingBookings: number;
  cancelledBookings: number;
  todayBookings: number;
  upcomingBookings: number;
  utilizationRate: number;
  cabinetUtilization: Array<{
    cabinetId: string;
    cabinetName: string;
    capacity: number;
    totalBookings: number;
    confirmedBookings: number;
    utilizationRate: number;
  }>;
  bookingTrends: Array<{
    date: string;
    bookingCount: number;
    confirmedCount: number;
  }>;
  popularTimeSlots: Array<{
    timeSlot: string;
    bookingCount: number;
  }>;
  topUsers: Array<{
    userId: string;
    userName: string;
    bookingCount: number;
  }>;
}

// GET /api/cabinets/stats - Get cabinet and booking statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view cabinet stats)
    if (!hasPermission(authResult.user.role, 'reports', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build date filter for bookings
    const dateConditions: string[] = [];
    const dateParams: any[] = [];
    let paramIndex = 1;

    if (dateFrom) {
      dateConditions.push(`cb.date >= $${paramIndex}`);
      dateParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      dateConditions.push(`cb.date <= $${paramIndex}`);
      dateParams.push(dateTo);
      paramIndex++;
    }

    const dateFilter = dateConditions.length > 0 ? `AND ${dateConditions.join(' AND ')}` : '';

    // Get cabinet statistics
    const cabinetStatsSql = `
      SELECT 
        COUNT(*) as total_cabinets,
        COUNT(CASE WHEN is_available = true THEN 1 END) as available_cabinets,
        COUNT(CASE WHEN is_available = false THEN 1 END) as unavailable_cabinets
      FROM cabinets
    `;

    const cabinetStatsResult = await query(cabinetStatsSql);
    const cabinetStats = cabinetStatsResult.rows[0];

    // Get booking statistics
    const bookingStatsSql = `
      SELECT 
        COUNT(*) as total_bookings,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_bookings,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_bookings,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
        COUNT(CASE WHEN date = CURRENT_DATE THEN 1 END) as today_bookings,
        COUNT(CASE WHEN date >= CURRENT_DATE AND status IN ('confirmed', 'pending') THEN 1 END) as upcoming_bookings
      FROM cabinet_bookings cb
      WHERE 1=1 ${dateFilter}
    `;

    const bookingStatsResult = await query(bookingStatsSql, dateParams);
    const bookingStats = bookingStatsResult.rows[0];

    // Calculate overall utilization rate
    const utilizationSql = `
      WITH total_slots AS (
        SELECT COUNT(*) * 24 as total_possible_hours
        FROM cabinets 
        WHERE is_available = true
      ),
      booked_hours AS (
        SELECT 
          SUM(
            EXTRACT(EPOCH FROM (end_time::time - start_time::time)) / 3600
          ) as total_booked_hours
        FROM cabinet_bookings cb
        JOIN cabinets c ON cb.cabinet_id = c.id
        WHERE c.is_available = true 
          AND cb.status = 'confirmed'
          ${dateFilter}
      )
      SELECT 
        COALESCE(
          CASE 
            WHEN ts.total_possible_hours > 0 
            THEN (bh.total_booked_hours / ts.total_possible_hours * 100)
            ELSE 0 
          END, 0
        ) as utilization_rate
      FROM total_slots ts, booked_hours bh
    `;

    const utilizationResult = await query(utilizationSql, dateParams);
    const utilizationRate = parseFloat(utilizationResult.rows[0].utilization_rate || 0);

    // Get cabinet utilization breakdown
    const cabinetUtilizationSql = `
      SELECT 
        c.id as cabinet_id,
        c.name as cabinet_name,
        c.capacity,
        COUNT(cb.id) as total_bookings,
        COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END) as confirmed_bookings,
        COALESCE(
          ROUND(
            (COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END)::DECIMAL / 
             NULLIF(COUNT(cb.id), 0)) * 100, 2
          ), 0
        ) as utilization_rate
      FROM cabinets c
      LEFT JOIN cabinet_bookings cb ON c.id = cb.cabinet_id ${dateFilter.replace('cb.date', 'cb.date')}
      WHERE c.is_available = true
      GROUP BY c.id, c.name, c.capacity
      ORDER BY utilization_rate DESC
    `;

    const cabinetUtilizationResult = await query(cabinetUtilizationSql, dateParams);
    const cabinetUtilization = cabinetUtilizationResult.rows.map(row => ({
      cabinetId: row.cabinet_id,
      cabinetName: row.cabinet_name,
      capacity: parseInt(row.capacity),
      totalBookings: parseInt(row.total_bookings),
      confirmedBookings: parseInt(row.confirmed_bookings),
      utilizationRate: parseFloat(row.utilization_rate)
    }));

    // Get booking trends (last 30 days or filtered period)
    const trendsSql = `
      SELECT 
        cb.date::text as date,
        COUNT(*) as booking_count,
        COUNT(CASE WHEN cb.status = 'confirmed' THEN 1 END) as confirmed_count
      FROM cabinet_bookings cb
      WHERE cb.date >= COALESCE($${dateParams.length > 0 ? 1 : 'NULL'}, CURRENT_DATE - INTERVAL '30 days')
        ${dateTo ? `AND cb.date <= $${dateParams.length}` : ''}
      GROUP BY cb.date
      ORDER BY cb.date DESC
      LIMIT 30
    `;

    const trendsResult = await query(trendsSql, dateParams);
    const bookingTrends = trendsResult.rows.map(row => ({
      date: row.date,
      bookingCount: parseInt(row.booking_count),
      confirmedCount: parseInt(row.confirmed_count)
    }));

    // Get popular time slots
    const timeSlotsSql = `
      SELECT 
        CONCAT(
          LPAD(EXTRACT(HOUR FROM start_time)::text, 2, '0'), 
          ':00-', 
          LPAD((EXTRACT(HOUR FROM start_time) + 1)::text, 2, '0'), 
          ':00'
        ) as time_slot,
        COUNT(*) as booking_count
      FROM cabinet_bookings cb
      WHERE status = 'confirmed' ${dateFilter}
      GROUP BY EXTRACT(HOUR FROM start_time)
      ORDER BY booking_count DESC
      LIMIT 10
    `;

    const timeSlotsResult = await query(timeSlotsSql, dateParams);
    const popularTimeSlots = timeSlotsResult.rows.map(row => ({
      timeSlot: row.time_slot,
      bookingCount: parseInt(row.booking_count)
    }));

    // Get top users by booking count
    const topUsersSql = `
      SELECT 
        cb.booked_by as user_id,
        u.name as user_name,
        COUNT(*) as booking_count
      FROM cabinet_bookings cb
      LEFT JOIN users u ON cb.booked_by = u.id
      WHERE cb.status = 'confirmed' ${dateFilter}
      GROUP BY cb.booked_by, u.name
      ORDER BY booking_count DESC
      LIMIT 10
    `;

    const topUsersResult = await query(topUsersSql, dateParams);
    const topUsers = topUsersResult.rows.map(row => ({
      userId: row.user_id,
      userName: row.user_name || 'Unknown User',
      bookingCount: parseInt(row.booking_count)
    }));

    const stats: CabinetStats = {
      totalCabinets: parseInt(cabinetStats.total_cabinets),
      availableCabinets: parseInt(cabinetStats.available_cabinets),
      unavailableCabinets: parseInt(cabinetStats.unavailable_cabinets),
      totalBookings: parseInt(bookingStats.total_bookings),
      confirmedBookings: parseInt(bookingStats.confirmed_bookings),
      pendingBookings: parseInt(bookingStats.pending_bookings),
      cancelledBookings: parseInt(bookingStats.cancelled_bookings),
      todayBookings: parseInt(bookingStats.today_bookings),
      upcomingBookings: parseInt(bookingStats.upcoming_bookings),
      utilizationRate,
      cabinetUtilization,
      bookingTrends,
      popularTimeSlots,
      topUsers
    };

    return createResponse(stats, true, 'Cabinet statistics retrieved successfully');

  } catch (error) {
    console.error('Error fetching cabinet statistics:', error);
    return createErrorResponse('Failed to fetch cabinet statistics', 500);
  }
}
