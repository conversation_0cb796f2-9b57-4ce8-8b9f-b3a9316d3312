/**
 * Payment Form Component
 * Form for recording new payments
 */

'use client';

import React, { useState } from 'react';
import { PaymentType, PaymentMethod, PaymentStatus } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface PaymentFormData {
  studentId: string;
  amount: string;
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  description: string;
  paymentDate: string;
}

interface PaymentFormProps {
  onSubmit: (data: PaymentFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  initialData?: Partial<PaymentFormData>;
}

export default function PaymentForm({
  onSubmit,
  onCancel,
  isLoading = false,
  initialData = {}
}: PaymentFormProps) {
  const [formData, setFormData] = useState<PaymentFormData>({
    studentId: initialData.studentId || '',
    amount: initialData.amount || '',
    paymentType: initialData.paymentType || PaymentType.TUITION,
    paymentMethod: initialData.paymentMethod || PaymentMethod.CASH,
    description: initialData.description || '',
    paymentDate: initialData.paymentDate || new Date().toISOString().split('T')[0]
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate student ID
    if (!formData.studentId.trim()) {
      newErrors.studentId = 'Student ID is required';
    }

    // Validate amount
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      } else if (amount < PAYMENT_CONFIG.MIN_AMOUNT) {
        newErrors.amount = `Amount must be at least $${PAYMENT_CONFIG.MIN_AMOUNT}`;
      } else if (amount > PAYMENT_CONFIG.MAX_AMOUNT) {
        newErrors.amount = `Amount cannot exceed $${PAYMENT_CONFIG.MAX_AMOUNT}`;
      }
    }

    // Validate payment date
    if (!formData.paymentDate) {
      newErrors.paymentDate = 'Payment date is required';
    } else {
      const paymentDate = new Date(formData.paymentDate);
      const today = new Date();
      if (paymentDate > today) {
        newErrors.paymentDate = 'Payment date cannot be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  const handleInputChange = (field: keyof PaymentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Student ID */}
      <div>
        <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 mb-1">
          Student ID *
        </label>
        <input
          type="text"
          id="studentId"
          value={formData.studentId}
          onChange={(e) => handleInputChange('studentId', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.studentId ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Enter student ID"
          disabled={isLoading}
        />
        {errors.studentId && (
          <p className="mt-1 text-sm text-red-600">{errors.studentId}</p>
        )}
      </div>

      {/* Amount */}
      <div>
        <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
          Amount ({PAYMENT_CONFIG.CURRENCY}) *
        </label>
        <input
          type="number"
          id="amount"
          step="0.01"
          min={PAYMENT_CONFIG.MIN_AMOUNT}
          max={PAYMENT_CONFIG.MAX_AMOUNT}
          value={formData.amount}
          onChange={(e) => handleInputChange('amount', e.target.value)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.amount ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="0.00"
          disabled={isLoading}
        />
        {errors.amount && (
          <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
        )}
      </div>

      {/* Payment Type */}
      <div>
        <label htmlFor="paymentType" className="block text-sm font-medium text-gray-700 mb-1">
          Payment Type *
        </label>
        <select
          id="paymentType"
          value={formData.paymentType}
          onChange={(e) => handleInputChange('paymentType', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        >
          <option value={PaymentType.TUITION}>Tuition</option>
          <option value={PaymentType.REGISTRATION}>Registration</option>
          <option value={PaymentType.MATERIALS}>Materials</option>
          <option value={PaymentType.EXAM_FEE}>Exam Fee</option>
          <option value={PaymentType.OTHER}>Other</option>
        </select>
      </div>

      {/* Payment Method */}
      <div>
        <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
          Payment Method *
        </label>
        <select
          id="paymentMethod"
          value={formData.paymentMethod}
          onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        >
          <option value={PaymentMethod.CASH}>Cash</option>
          <option value={PaymentMethod.CARD}>Card</option>
          <option value={PaymentMethod.BANK_TRANSFER}>Bank Transfer</option>
          <option value={PaymentMethod.ONLINE}>Online</option>
          <option value={PaymentMethod.OTHER}>Other</option>
        </select>
      </div>

      {/* Payment Date */}
      <div>
        <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700 mb-1">
          Payment Date *
        </label>
        <input
          type="date"
          id="paymentDate"
          value={formData.paymentDate}
          onChange={(e) => handleInputChange('paymentDate', e.target.value)}
          max={new Date().toISOString().split('T')[0]}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.paymentDate ? 'border-red-500' : 'border-gray-300'
          }`}
          disabled={isLoading}
        />
        {errors.paymentDate && (
          <p className="mt-1 text-sm text-red-600">{errors.paymentDate}</p>
        )}
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          id="description"
          rows={3}
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Optional description or notes"
          disabled={isLoading}
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          {isLoading ? 'Recording...' : 'Record Payment'}
        </button>
      </div>
    </form>
  );
}
