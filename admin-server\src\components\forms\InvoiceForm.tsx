/**
 * Invoice Form Component
 * Form for creating new invoices
 */

'use client';

import React, { useState } from 'react';
import { InvoiceStatus } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface InvoiceFormData {
  studentId: string;
  amount: string;
  dueDate: string;
}

interface InvoiceFormProps {
  onSubmit: (data: InvoiceFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  initialData?: Partial<InvoiceFormData>;
}

export default function InvoiceForm({
  onSubmit,
  onCancel,
  isLoading = false,
  initialData = {}
}: InvoiceFormProps) {
  const [formData, setFormData] = useState<InvoiceFormData>({
    studentId: initialData.studentId || '',
    amount: initialData.amount || '',
    dueDate: initialData.dueDate || (() => {
      const date = new Date();
      date.setDate(date.getDate() + 30); // Default to 30 days from now
      return date.toISOString().split('T')[0];
    })()
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate student ID
    if (!formData.studentId.trim()) {
      newErrors.studentId = 'Student ID is required';
    }

    // Validate amount
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      } else if (amount < PAYMENT_CONFIG.MIN_AMOUNT) {
        newErrors.amount = `Amount must be at least $${PAYMENT_CONFIG.MIN_AMOUNT}`;
      } else if (amount > PAYMENT_CONFIG.MAX_AMOUNT) {
        newErrors.amount = `Amount cannot exceed $${PAYMENT_CONFIG.MAX_AMOUNT}`;
      }
    }

    // Validate due date
    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required';
    } else {
      const dueDate = new Date(formData.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (dueDate < today) {
        newErrors.dueDate = 'Due date cannot be in the past';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting invoice:', error);
    }
  };

  const handleInputChange = (field: keyof InvoiceFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 1); // Max 1 year in future
    return maxDate.toISOString().split('T')[0];
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Create New Invoice</h3>
        <p className="mt-1 text-sm text-gray-600">
          Generate an invoice for a student with a specified due date.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Student ID */}
        <div>
          <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 mb-1">
            Student ID *
          </label>
          <input
            type="text"
            id="studentId"
            value={formData.studentId}
            onChange={(e) => handleInputChange('studentId', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.studentId ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter student ID"
            disabled={isLoading}
          />
          {errors.studentId && (
            <p className="mt-1 text-sm text-red-600">{errors.studentId}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            The unique identifier for the student receiving this invoice.
          </p>
        </div>

        {/* Amount */}
        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
            Amount ({PAYMENT_CONFIG.CURRENCY}) *
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">$</span>
            </div>
            <input
              type="number"
              id="amount"
              step="0.01"
              min={PAYMENT_CONFIG.MIN_AMOUNT}
              max={PAYMENT_CONFIG.MAX_AMOUNT}
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              className={`w-full pl-7 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.amount ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
              disabled={isLoading}
            />
          </div>
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Amount range: ${PAYMENT_CONFIG.MIN_AMOUNT} - ${PAYMENT_CONFIG.MAX_AMOUNT.toLocaleString()}
          </p>
        </div>

        {/* Due Date */}
        <div>
          <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
            Due Date *
          </label>
          <input
            type="date"
            id="dueDate"
            value={formData.dueDate}
            onChange={(e) => handleInputChange('dueDate', e.target.value)}
            min={getMinDate()}
            max={getMaxDate()}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.dueDate ? 'border-red-500' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
          {errors.dueDate && (
            <p className="mt-1 text-sm text-red-600">{errors.dueDate}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            The date by which this invoice should be paid.
          </p>
        </div>

        {/* Invoice Preview */}
        <div className="bg-gray-50 p-4 rounded-md border">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Invoice Preview</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Student:</span>
              <span className="font-medium">{formData.studentId || 'Not specified'}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span className="font-medium">
                {formData.amount ? `$${parseFloat(formData.amount).toFixed(2)}` : '$0.00'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Due Date:</span>
              <span className="font-medium">
                {formData.dueDate ? new Date(formData.dueDate).toLocaleDateString() : 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Status:</span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pending
              </span>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </div>
            ) : (
              'Create Invoice'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
