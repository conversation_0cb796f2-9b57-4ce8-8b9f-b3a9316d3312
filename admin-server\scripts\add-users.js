/**
 * <PERSON><PERSON><PERSON> to add test users for the admin system
 * Creates admin, cashier, and accountant users with working credentials
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

// Define test users with working credentials
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'admin',
    name: 'System Administrator'
  },
  {
    email: '<EMAIL>',
    password: 'Cashier123!',
    role: 'cashier',
    name: 'Main Cashier'
  },
  {
    email: '<EMAIL>',
    password: 'Accountant123!',
    role: 'accountant',
    name: 'Chief Accountant'
  }
];

async function addUsers() {
  console.log('👥 Adding test users to the database...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', testResult.rows[0].current_time);
    
    // Check existing users
    console.log('🔍 Checking existing users...');
    const existingUsersResult = await pool.query('SELECT email, role FROM users');
    const existingUsers = existingUsersResult.rows;
    console.log('📋 Existing users:', existingUsers);
    
    // Add missing users
    for (const user of testUsers) {
      const userExists = existingUsers.some(existing => existing.email === user.email);
      
      if (!userExists) {
        console.log(`➕ Creating user: ${user.email} (${user.role})`);
        const passwordHash = await bcrypt.hash(user.password, 12);
        
        await pool.query(
          'INSERT INTO users (email, password_hash, role, name) VALUES ($1, $2, $3, $4)',
          [user.email, passwordHash, user.role, user.name]
        );
        
        console.log(`✅ User created: ${user.email}`);
      } else {
        console.log(`👤 User already exists: ${user.email}`);
      }
    }
    
    // Display final user list
    console.log('\n👥 Final user list:');
    const finalUsersResult = await pool.query('SELECT email, role, name, is_active FROM users ORDER BY role, email');
    finalUsersResult.rows.forEach(user => {
      console.log(`  ${user.role.toUpperCase()}: ${user.email} (${user.name}) - ${user.is_active ? 'Active' : 'Inactive'}`);
    });
    
    console.log('\n🔑 Working Credentials for Testing:');
    console.log('=====================================');
    testUsers.forEach(user => {
      console.log(`${user.role.toUpperCase()}:`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Password: ${user.password}`);
      console.log('');
    });
    
    console.log('🎉 User setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Failed to add users:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Check if DATABASE_URL is provided
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  console.log('💡 Make sure you have .env.local file with DATABASE_URL');
  process.exit(1);
}

addUsers();
