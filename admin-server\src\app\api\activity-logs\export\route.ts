/**
 * Activity Logs Export API endpoint
 * Handles exporting activity logs in various formats (CSV, JSON)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logReportOperation, getRequestContext } from '@/lib/activity-logger';
import { ActivityAction, ResourceType } from '@/types';

interface ActivityLogWithUser {
  id: string;
  user_id: string;
  action: ActivityAction;
  resource_type: ResourceType;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  description?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
}

// Convert activity logs to CSV format
function convertToCSV(logs: any[]): string {
  const headers = [
    'ID', 'Timestamp', 'User Name', 'User Email', 'User Role',
    'Action', 'Resource Type', 'Resource ID', 'Description',
    'IP Address', 'User Agent'
  ];

  const csvRows = [headers.join(',')];

  logs.forEach(log => {
    const row = [
      log.id,
      log.timestamp,
      log.user?.name || 'Unknown',
      log.user?.email || 'Unknown',
      log.user?.role || 'Unknown',
      log.action,
      log.resourceType,
      log.resourceId || '',
      `"${(log.description || '').replace(/"/g, '""')}"`,
      log.ipAddress || '',
      `"${(log.userAgent || '').replace(/"/g, '""')}"`
    ];
    csvRows.push(row.join(','));
  });

  return csvRows.join('\n');
}

// GET /api/activity-logs/export - Export activity logs
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can export activity logs)
    if (!hasPermission(authResult.user.role, 'activity-logs', 'export')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';
    const filters = parseFilterParams(searchParams);
    const context = getRequestContext(request.headers);

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      return createErrorResponse('Invalid format. Supported formats: csv, json', 400);
    }

    try {
      // Build query with filters (similar to main activity logs endpoint)
      let whereClause = 'WHERE 1=1';
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (filters.userId) {
        whereClause += ` AND al.user_id = $${paramIndex}`;
        queryParams.push(filters.userId);
        paramIndex++;
      }

      if (filters.action) {
        whereClause += ` AND al.action = $${paramIndex}`;
        queryParams.push(filters.action);
        paramIndex++;
      }

      if (filters.resourceType) {
        whereClause += ` AND al.resource_type = $${paramIndex}`;
        queryParams.push(filters.resourceType);
        paramIndex++;
      }

      if (filters.dateFrom) {
        whereClause += ` AND al.timestamp >= $${paramIndex}`;
        queryParams.push(filters.dateFrom);
        paramIndex++;
      }

      if (filters.dateTo) {
        whereClause += ` AND al.timestamp <= $${paramIndex}`;
        queryParams.push(filters.dateTo);
        paramIndex++;
      }

      // Limit export to prevent memory issues (max 10,000 records)
      const maxRecords = 10000;
      
      // Get activity logs for export
      const logsResult = await query<ActivityLogWithUser>(
        `SELECT 
           al.id, al.user_id, al.action, al.resource_type, al.resource_id,
           al.old_values, al.new_values, al.ip_address, al.user_agent,
           al.timestamp, al.description,
           u.name as user_name, u.email as user_email, u.role as user_role
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ${whereClause} 
         ORDER BY al.timestamp DESC 
         LIMIT ${maxRecords}`,
        queryParams
      );

      // Format the logs
      const formattedLogs = logsResult.rows.map(log => ({
        id: log.id,
        userId: log.user_id,
        action: log.action,
        resourceType: log.resource_type,
        resourceId: log.resource_id,
        oldValues: log.old_values,
        newValues: log.new_values,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.timestamp,
        description: log.description,
        user: log.user_name ? {
          name: log.user_name,
          email: log.user_email,
          role: log.user_role
        } : null
      }));

      // Log the export operation
      await logReportOperation('EXPORT', authResult.user.id, 'activity-logs', context);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `activity-logs-${timestamp}.${format}`;

      if (format === 'csv') {
        const csvContent = convertToCSV(formattedLogs);
        
        return new Response(csvContent, {
          status: 200,
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Cache-Control': 'no-cache',
          },
        });
      } else if (format === 'json') {
        const jsonContent = JSON.stringify({
          exportedAt: new Date().toISOString(),
          exportedBy: authResult.user.email,
          totalRecords: formattedLogs.length,
          filters: filters,
          logs: formattedLogs
        }, null, 2);

        return new Response(jsonContent, {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Cache-Control': 'no-cache',
          },
        });
      }

    } catch (dbError) {
      console.error('Database error exporting activity logs:', dbError);
      
      // For development, return mock export if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockLogs = [
          {
            id: 'mock-log-1',
            userId: 'mock-admin-id',
            action: 'LOGIN' as ActivityAction,
            resourceType: 'USER' as ResourceType,
            timestamp: new Date(),
            description: 'User logged in',
            user: { name: 'Mock User', email: '<EMAIL>', role: 'admin' }
          }
        ];

        if (format === 'csv') {
          const csvContent = convertToCSV(mockLogs);
          return new Response(csvContent, {
            status: 200,
            headers: {
              'Content-Type': 'text/csv',
              'Content-Disposition': 'attachment; filename="activity-logs-mock.csv"',
            },
          });
        } else {
          const jsonContent = JSON.stringify({
            exportedAt: new Date().toISOString(),
            exportedBy: authResult.user.email,
            totalRecords: mockLogs.length,
            logs: mockLogs,
            note: 'Development mode - mock data'
          }, null, 2);

          return new Response(jsonContent, {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Content-Disposition': 'attachment; filename="activity-logs-mock.json"',
            },
          });
        }
      }
      
      return createErrorResponse('Activity log export service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Export activity logs error:', error);
    return createErrorResponse('Failed to export activity logs', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
