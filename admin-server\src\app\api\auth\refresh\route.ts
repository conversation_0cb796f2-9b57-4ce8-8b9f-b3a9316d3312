/**
 * Token Refresh API endpoint
 * Handles JWT token refresh using refresh tokens
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { refreshToken as refreshJwtToken } from '@/lib/auth';

interface RefreshRequest {
  refreshToken: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: RefreshRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['refreshToken']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { refreshToken } = body;

    // Refresh the token
    const newTokens = refreshJwtToken(refreshToken);
    
    if (!newTokens) {
      return createErrorResponse('Invalid or expired refresh token', 401);
    }

    return createResponse({
      token: newTokens.token,
      refreshToken: newTokens.refreshToken,
      expiresIn: newTokens.expiresIn
    }, true, 'Token refreshed successfully');

  } catch (error) {
    console.error('Token refresh error:', error);
    return createErrorResponse(
      'Token refresh failed',
      500
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
