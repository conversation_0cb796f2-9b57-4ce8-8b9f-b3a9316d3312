/**
 * Type definitions for Admin Server
 * Re-exports shared types and defines admin-specific types
 */

// Re-export shared types
export * from '../../../shared/types/common';
export * from '../../../shared/types/user';
export * from '../../../shared/types/activity-log';
export * from '../../../shared/types/api';

// Admin server specific types
export interface AdminDashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalPayments: number;
  totalRevenue: number;
  pendingInvoices: number;
  activeBookings: number;
  recentActivityCount: number;
}

export interface AdminFinancialSummary {
  totalRevenue: number;
  monthlyRevenue: number;
  weeklyRevenue: number;
  dailyRevenue: number;
  pendingAmount: number;
  completedPayments: number;
  pendingInvoices: number;
  overdueInvoices: number;
  revenueGrowth: number;
  paymentsByType: Record<string, number>;
  paymentsByMethod: Record<string, number>;
}

export interface AdminUserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  usersByRole: Record<string, number>;
  recentRegistrations: number;
  loginActivity: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}

export interface AdminCabinetStats {
  totalCabinets: number;
  availableCabinets: number;
  bookedCabinets: number;
  utilizationRate: number;
  bookingsByStatus: Record<string, number>;
  upcomingBookings: number;
  todayBookings: number;
}

export interface AdminActivitySummary {
  totalActions: number;
  todayActions: number;
  uniqueActiveUsers: number;
  mostActiveUser: {
    userId: string;
    userName: string;
    actionCount: number;
  };
  actionsByType: Record<string, number>;
  recentCriticalActions: Array<{
    id: string;
    action: string;
    resourceType: string;
    userName: string;
    timestamp: Date;
    description: string;
  }>;
}

// Form validation types
export interface FormValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: FormValidationError[];
}

// API request/response types specific to admin server
export interface AdminLoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AdminLoginResponse {
  success: boolean;
  user: {
    id: string;
    email: string;
    role: string;
    name: string;
  };
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// Table column definitions
export interface TableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  className?: string;
}

// Chart data types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}

// Navigation types
export interface NavigationItem {
  label: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
  children?: NavigationItem[];
  requiredRoles?: string[];
}

// Modal types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Toast notification types
export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// File upload types
export interface FileUploadResult {
  success: boolean;
  fileName?: string;
  fileUrl?: string;
  error?: string;
}

// Export/Import types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  dateRange?: {
    from: string;
    to: string;
  };
  filters?: Record<string, any>;
  columns?: string[];
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  errors: Array<{
    row: number;
    error: string;
  }>;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
  role?: string;
  type?: string;
  [key: string]: any;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

// Settings types
export interface AdminSettings {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    dateFormat: string;
    currency: string;
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireTwoFactor: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
  };
  activityLogging: {
    enabled: boolean;
    retentionDays: number;
    logSensitiveData: boolean;
  };
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export interface EmptyStateProps extends BaseComponentProps {
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ComponentType<any>;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppError;
}

// Theme types
export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  fonts: {
    primary: string;
    secondary: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}
