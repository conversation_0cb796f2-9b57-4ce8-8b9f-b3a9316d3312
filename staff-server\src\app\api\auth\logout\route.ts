/**
 * Staff authentication logout endpoint
 * Handles logout for staff users with activity logging
 */

import { NextRequest } from 'next/server';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Get request context for logging
    const context = getRequestContext(request.headers);

    try {
      // Log logout event
      await logAuthEvent('LOGOUT', authResult.user.id, context);

      return createResponse(
        { message: 'Logged out successfully' },
        true,
        'Logout successful'
      );

    } catch (logError) {
      console.error('Error logging logout event:', logError);
      // Still return success even if logging fails
      return createResponse(
        { message: 'Logged out successfully' },
        true,
        'Logout successful'
      );
    }

  } catch (error) {
    console.error('Staff logout error:', error);
    return createErrorResponse('Logout failed', 500);
  }
}
