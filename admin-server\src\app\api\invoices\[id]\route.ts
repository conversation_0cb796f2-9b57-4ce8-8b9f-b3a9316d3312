/**
 * Individual Invoice API endpoint
 * Handles operations on specific invoices (GET, PUT)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logInvoiceOperation, getRequestContext } from '@/lib/activity-logger';
import { InvoiceStatus, ActivityAction } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface Invoice {
  id: string;
  student_id: string;
  amount: number;
  due_date: Date;
  paid_date?: Date;
  status: InvoiceStatus;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

interface InvoiceWithUser extends Invoice {
  created_by_name?: string;
  created_by_email?: string;
}

interface UpdateInvoiceRequest {
  amount?: number;
  dueDate?: string;
  paidDate?: string;
  status?: InvoiceStatus;
}

// GET /api/invoices/[id] - Get invoice by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'invoices', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid invoice ID format', 400);
    }

    // Get invoice with user information
    const sql = `
      SELECT 
        i.id,
        i.student_id,
        i.amount,
        i.due_date,
        i.paid_date,
        i.status,
        i.created_by,
        i.created_at,
        i.updated_at,
        u.name as created_by_name,
        u.email as created_by_email
      FROM invoices i
      LEFT JOIN users u ON i.created_by = u.id
      WHERE i.id = $1
    `;

    const result = await query<InvoiceWithUser>(sql, [id]);

    if (result.rows.length === 0) {
      return createErrorResponse('Invoice not found', 404);
    }

    const invoice = result.rows[0];

    // Format response
    const responseInvoice = {
      id: invoice.id,
      studentId: invoice.student_id,
      amount: invoice.amount,
      dueDate: invoice.due_date,
      paidDate: invoice.paid_date,
      status: invoice.status,
      createdBy: invoice.created_by,
      createdAt: invoice.created_at,
      updatedAt: invoice.updated_at,
      createdByUser: invoice.created_by_name ? {
        id: invoice.created_by,
        name: invoice.created_by_name,
        email: invoice.created_by_email
      } : undefined
    };

    return createResponse(responseInvoice, true, 'Invoice retrieved successfully');

  } catch (error) {
    console.error('Error fetching invoice:', error);
    return createErrorResponse('Failed to fetch invoice', 500);
  }
}

// PUT /api/invoices/[id] - Update invoice
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'invoices', 'update')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid invoice ID format', 400);
    }

    const body: UpdateInvoiceRequest = await request.json();

    // Get current invoice data
    const currentSql = `
      SELECT id, student_id, amount, due_date, paid_date, 
             status, created_by, created_at, updated_at
      FROM invoices 
      WHERE id = $1
    `;

    const currentResult = await query<Invoice>(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Invoice not found', 404);
    }

    const currentInvoice = currentResult.rows[0];

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateParams: any[] = [];
    let paramIndex = 1;

    if (body.amount !== undefined) {
      if (body.amount < PAYMENT_CONFIG.MIN_AMOUNT || body.amount > PAYMENT_CONFIG.MAX_AMOUNT) {
        return createErrorResponse(
          `Amount must be between $${PAYMENT_CONFIG.MIN_AMOUNT} and $${PAYMENT_CONFIG.MAX_AMOUNT}`,
          400
        );
      }
      updateFields.push(`amount = $${paramIndex}`);
      updateParams.push(body.amount);
      paramIndex++;
    }

    if (body.dueDate !== undefined) {
      const processedDueDate = new Date(body.dueDate);
      if (isNaN(processedDueDate.getTime())) {
        return createErrorResponse('Invalid due date format', 400);
      }
      updateFields.push(`due_date = $${paramIndex}`);
      updateParams.push(processedDueDate);
      paramIndex++;
    }

    if (body.paidDate !== undefined) {
      if (body.paidDate) {
        const processedPaidDate = new Date(body.paidDate);
        if (isNaN(processedPaidDate.getTime())) {
          return createErrorResponse('Invalid paid date format', 400);
        }
        updateFields.push(`paid_date = $${paramIndex}`);
        updateParams.push(processedPaidDate);
        paramIndex++;
      } else {
        updateFields.push(`paid_date = NULL`);
      }
    }

    if (body.status !== undefined) {
      if (!Object.values(InvoiceStatus).includes(body.status)) {
        return createErrorResponse('Invalid invoice status', 400);
      }
      
      // Auto-set paid_date when marking as paid
      if (body.status === InvoiceStatus.PAID && !currentInvoice.paid_date && body.paidDate === undefined) {
        updateFields.push(`paid_date = CURRENT_DATE`);
      }
      
      // Clear paid_date when marking as not paid
      if (body.status !== InvoiceStatus.PAID && currentInvoice.paid_date && body.paidDate === undefined) {
        updateFields.push(`paid_date = NULL`);
      }
      
      updateFields.push(`status = $${paramIndex}`);
      updateParams.push(body.status);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', 400);
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE clause parameter
    updateParams.push(id);

    // Execute update
    const updateSql = `
      UPDATE invoices 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, student_id, amount, due_date, paid_date, 
                status, created_by, created_at, updated_at
    `;

    const updateResult = await query<Invoice>(updateSql, updateParams);
    const updatedInvoice = updateResult.rows[0];

    // Log the invoice update
    const context = getRequestContext(request.headers);
    await logInvoiceOperation(
      ActivityAction.UPDATE,
      authResult.user.id,
      {
        id: updatedInvoice.id,
        studentId: updatedInvoice.student_id,
        amount: updatedInvoice.amount,
        dueDate: updatedInvoice.due_date,
        paidDate: updatedInvoice.paid_date,
        status: updatedInvoice.status
      },
      {
        id: currentInvoice.id,
        studentId: currentInvoice.student_id,
        amount: currentInvoice.amount,
        dueDate: currentInvoice.due_date,
        paidDate: currentInvoice.paid_date,
        status: currentInvoice.status
      },
      context
    );

    // Format response
    const responseInvoice = {
      id: updatedInvoice.id,
      studentId: updatedInvoice.student_id,
      amount: updatedInvoice.amount,
      dueDate: updatedInvoice.due_date,
      paidDate: updatedInvoice.paid_date,
      status: updatedInvoice.status,
      createdBy: updatedInvoice.created_by,
      createdAt: updatedInvoice.created_at,
      updatedAt: updatedInvoice.updated_at
    };

    return createResponse(responseInvoice, true, 'Invoice updated successfully');

  } catch (error) {
    console.error('Error updating invoice:', error);
    return createErrorResponse('Failed to update invoice', 500);
  }
}
