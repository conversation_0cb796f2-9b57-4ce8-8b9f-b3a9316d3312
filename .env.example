# ===========================================
# INNOVATIVE CENTRE PLATFORM - ENVIRONMENT VARIABLES
# ===========================================

# Copy this file to .env.local in each server directory and configure the values

# ===========================================
# ADMIN SERVER CONFIGURATION
# ===========================================

# Database Configuration
ADMIN_DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require

# Authentication
ADMIN_JWT_SECRET=your-admin-jwt-secret-key-here
ADMIN_NEXTAUTH_SECRET=your-admin-nextauth-secret-here

# App Configuration
ADMIN_APP_URL=http://localhost:3000
ADMIN_PORT=3000

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365

# ===========================================
# STAFF SERVER CONFIGURATION
# ===========================================

# Database Configuration
STAFF_DATABASE_URL=postgresql://staff_owner:<EMAIL>/staff?sslmode=require

# Authentication
STAFF_JWT_SECRET=your-staff-jwt-secret-key-here
STAFF_NEXTAUTH_SECRET=your-staff-nextauth-secret-here

# App Configuration
STAFF_APP_URL=http://localhost:3001
STAFF_PORT=3001

# Activity Logging
STAFF_ENABLE_ACTIVITY_LOGGING=true
STAFF_LOG_RETENTION_DAYS=365

# ===========================================
# SHARED CONFIGURATION
# ===========================================

# Environment
NODE_ENV=development

# Security
BCRYPT_ROUNDS=12
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Email Configuration (if needed)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
FROM_EMAIL=<EMAIL>

# ===========================================
# PRODUCTION OVERRIDES
# ===========================================

# Set these in production environment
# ADMIN_APP_URL=https://admin.innovativecentre.com
# STAFF_APP_URL=https://staff.innovativecentre.com
# NODE_ENV=production
