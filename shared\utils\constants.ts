/**
 * Constants for the Innovative Centre Platform
 */

// Application constants
export const APP_CONFIG = {
  NAME: 'Innovative Centre Platform',
  VERSION: '1.0.0',
  DESCRIPTION: 'Comprehensive CRM for English tutoring organization',
  ADMIN_PORT: 3000,
  STAFF_PORT: 3001
} as const;

// Database constants
export const DATABASE_CONFIG = {
  MAX_CONNECTIONS: 20,
  CONNECTION_TIMEOUT: 30000,
  QUERY_TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
} as const;

// Authentication constants
export const AUTH_CONFIG = {
  JWT_EXPIRES_IN: '24h',
  REFRESH_TOKEN_EXPIRES_IN: '7d',
  BCRYPT_ROUNDS: 12,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000 // 24 hours
} as const;

// Activity logging constants
export const ACTIVITY_LOG_CONFIG = {
  DEFAULT_RETENTION_DAYS: 365,
  MAX_LOG_SIZE: 1000000, // 1MB
  BATCH_SIZE: 100,
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000 // 24 hours
} as const;

// Pagination constants
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 5
} as const;

// File upload constants
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  UPLOAD_PATH: '/uploads'
} as const;

// Rate limiting constants
export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  MAX_REQUESTS: 100,
  SKIP_SUCCESSFUL_REQUESTS: false,
  SKIP_FAILED_REQUESTS: false
} as const;

// Email configuration
export const EMAIL_CONFIG = {
  FROM_NAME: 'Innovative Centre',
  FROM_EMAIL: '<EMAIL>',
  SMTP_TIMEOUT: 10000,
  MAX_RETRIES: 3
} as const;

// Payment constants
export const PAYMENT_CONFIG = {
  MIN_AMOUNT: 0.01,
  MAX_AMOUNT: 999999.99,
  CURRENCY: 'USD',
  DECIMAL_PLACES: 2
} as const;

// Cabinet booking constants
export const BOOKING_CONFIG = {
  MIN_BOOKING_DURATION: 30, // minutes
  MAX_BOOKING_DURATION: 8 * 60, // 8 hours
  ADVANCE_BOOKING_DAYS: 30,
  CANCELLATION_HOURS: 24
} as const;

// KPI constants
export const KPI_CONFIG = {
  MIN_RETENTION_RATE: 0,
  MAX_RETENTION_RATE: 100,
  MIN_PERFORMANCE_SCORE: 0,
  MAX_PERFORMANCE_SCORE: 100,
  MIN_SATISFACTION_SCORE: 0,
  MAX_SATISFACTION_SCORE: 10
} as const;

// Error messages
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  NOT_FOUND: 'Resource not found',
  VALIDATION_ERROR: 'Validation failed',
  DUPLICATE_ENTRY: 'Resource already exists',
  DATABASE_ERROR: 'Database operation failed',
  INTERNAL_ERROR: 'Internal server error',
  INVALID_CREDENTIALS: 'Invalid email or password',
  ACCOUNT_LOCKED: 'Account temporarily locked due to multiple failed login attempts',
  TOKEN_EXPIRED: 'Authentication token has expired',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for this operation'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deactivated successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  PAYMENT_CREATED: 'Payment recorded successfully',
  PAYMENT_UPDATED: 'Payment updated successfully',
  INVOICE_CREATED: 'Invoice generated successfully',
  INVOICE_UPDATED: 'Invoice updated successfully',
  CABINET_CREATED: 'Cabinet created successfully',
  CABINET_UPDATED: 'Cabinet updated successfully',
  BOOKING_CREATED: 'Booking created successfully',
  BOOKING_UPDATED: 'Booking updated successfully',
  BOOKING_CANCELLED: 'Booking cancelled successfully'
} as const;

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH_LOGIN: '/api/auth/login',
  AUTH_LOGOUT: '/api/auth/logout',
  AUTH_REFRESH: '/api/auth/refresh',
  AUTH_ME: '/api/auth/me',
  
  // Users
  USERS: '/api/users',
  USER_BY_ID: '/api/users/:id',
  
  // Payments
  PAYMENTS: '/api/payments',
  PAYMENT_BY_ID: '/api/payments/:id',
  
  // Invoices
  INVOICES: '/api/invoices',
  INVOICE_BY_ID: '/api/invoices/:id',
  
  // Cabinets
  CABINETS: '/api/cabinets',
  CABINET_BY_ID: '/api/cabinets/:id',
  CABINET_BOOKINGS: '/api/cabinets/:id/bookings',
  CABINET_BOOK: '/api/cabinets/:id/book',
  
  // Activity logs
  ACTIVITY_LOGS: '/api/activity-logs',
  ACTIVITY_LOG_BY_ID: '/api/activity-logs/:id',
  ACTIVITY_LOGS_USER: '/api/activity-logs/user/:userId',
  ACTIVITY_LOGS_EXPORT: '/api/activity-logs/export',
  
  // KPIs
  TEACHER_KPIS: '/api/kpis/teachers',
  RECEPTION_KPIS: '/api/kpis/reception',
  
  // Reports
  REPORTS: '/api/reports',
  REPORT_BY_ID: '/api/reports/:id'
} as const;

// HTTP headers
export const HTTP_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  USER_AGENT: 'User-Agent',
  X_FORWARDED_FOR: 'X-Forwarded-For',
  X_REAL_IP: 'X-Real-IP',
  X_REQUEST_ID: 'X-Request-ID'
} as const;

// Date formats
export const DATE_FORMATS = {
  ISO_DATE: 'YYYY-MM-DD',
  ISO_DATETIME: 'YYYY-MM-DDTHH:mm:ss.sssZ',
  DISPLAY_DATE: 'MMM DD, YYYY',
  DISPLAY_DATETIME: 'MMM DD, YYYY HH:mm',
  TIME_ONLY: 'HH:mm'
} as const;

// Regular expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  TIME: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  NUMERIC: /^\d+$/
} as const;
