/**
 * Lead management endpoints
 * Handles CRUD operations for lead capture, assignment, and conversion tracking
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidEmail,
  formatPhoneNumber,
  isValidUUID
} from '@/lib/utils';
import { logLeadOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole, LeadStatus } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface Lead {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  source?: string;
  status: LeadStatus;
  assigned_to?: string;
  created_at: Date;
  updated_at: Date;
}

interface LeadWithAssignee extends Lead {
  assigned_to_name?: string;
  assigned_to_email?: string;
}

// GET /api/leads - List leads with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - management and reception can view leads
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      // Handle search across multiple fields
      if (filters.search) {
        const searchTerm = `%${filters.search}%`;
        const searchResult = await query<LeadWithAssignee>(
          `SELECT COUNT(*) as total FROM leads l
           LEFT JOIN users u ON l.assigned_to = u.id
           WHERE (l.first_name ILIKE $1 OR l.last_name ILIKE $1 OR l.email ILIKE $1 OR l.phone ILIKE $1 OR l.source ILIKE $1)`,
          [searchTerm]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const leadsResult = await query<LeadWithAssignee>(
          `SELECT l.id, l.first_name, l.last_name, l.email, l.phone, l.source, 
                  l.status, l.assigned_to, l.created_at, l.updated_at,
                  u.name as assigned_to_name, u.email as assigned_to_email
           FROM leads l
           LEFT JOIN users u ON l.assigned_to = u.id
           WHERE (l.first_name ILIKE $1 OR l.last_name ILIKE $1 OR l.email ILIKE $1 OR l.phone ILIKE $1 OR l.source ILIKE $1)
           ORDER BY l.created_at DESC 
           LIMIT $2 OFFSET $3`,
          [searchTerm, pagination.limit, offset]
        );

        return createResponse({
          leads: leadsResult.rows,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Leads retrieved successfully');
      }

      // Add status filter
      if (filters.status && ['new', 'contacted', 'interested', 'enrolled', 'rejected'].includes(filters.status)) {
        conditions['l.status'] = filters.status;
      }

      // Add assigned user filter
      if (filters.assignedTo && isValidUUID(filters.assignedTo)) {
        conditions['l.assigned_to'] = filters.assignedTo;
      }

      // Add source filter
      if (filters.source) {
        conditions['l.source'] = filters.source;
      }

      // Add date range filters
      if (filters.dateFrom) {
        conditions['l.created_at >='] = filters.dateFrom;
      }

      if (filters.dateTo) {
        conditions['l.created_at <='] = filters.dateTo;
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM leads l ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results with assignee information
      const offset = (pagination.page - 1) * pagination.limit;
      const dataSql = `
        SELECT l.id, l.first_name, l.last_name, l.email, l.phone, l.source, 
               l.status, l.assigned_to, l.created_at, l.updated_at,
               u.name as assigned_to_name, u.email as assigned_to_email
        FROM leads l
        LEFT JOIN users u ON l.assigned_to = u.id
        ${whereClause} 
        ORDER BY l.created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const leadsResult = await query<LeadWithAssignee>(dataSql, [...params, pagination.limit, offset]);

      return createResponse({
        leads: leadsResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Leads retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving leads:', dbError);
      return createErrorResponse('Failed to retrieve leads', 500);
    }

  } catch (error) {
    console.error('Get leads error:', error);
    return createErrorResponse('Failed to retrieve leads', 500);
  }
}

// POST /api/leads - Create new lead
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - management and reception can create leads
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      source,
      status = 'new',
      assignedTo
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['firstName', 'lastName']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate status
    if (!['new', 'contacted', 'interested', 'enrolled', 'rejected'].includes(status)) {
      return createErrorResponse('Invalid status. Must be new, contacted, interested, enrolled, or rejected', 400);
    }

    // Validate assignedTo if provided
    if (assignedTo && !isValidUUID(assignedTo)) {
      return createErrorResponse('Invalid assigned user ID format', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if assigned user exists and is reception or management
      if (assignedTo) {
        const assigneeResult = await query(
          'SELECT id, role FROM users WHERE id = $1 AND is_active = true',
          [assignedTo]
        );

        if (assigneeResult.rows.length === 0) {
          return createErrorResponse('Assigned user not found or inactive', 400);
        }

        const assignee = assigneeResult.rows[0];
        if (!['management', 'reception'].includes(assignee.role)) {
          return createErrorResponse('Can only assign leads to management or reception staff', 400);
        }
      }

      // Check if lead with same email already exists (if email provided)
      if (email) {
        const existingLeadResult = await query(
          'SELECT id FROM leads WHERE email = $1',
          [email.toLowerCase()]
        );

        if (existingLeadResult.rows.length > 0) {
          return createErrorResponse('Lead with this email already exists', 409);
        }
      }

      // Create lead
      const leadResult = await query<Lead>(
        `INSERT INTO leads (first_name, last_name, email, phone, source, status, assigned_to)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id, first_name, last_name, email, phone, source, status, assigned_to, created_at, updated_at`,
        [
          firstName.trim(),
          lastName.trim(),
          email ? email.toLowerCase() : null,
          phone ? formatPhoneNumber(phone) : null,
          source?.trim() || null,
          status,
          assignedTo || null
        ]
      );

      const newLead = leadResult.rows[0];

      // Get assignee information if assigned
      let assigneeInfo = null;
      if (newLead.assigned_to) {
        const assigneeResult = await query(
          'SELECT name, email FROM users WHERE id = $1',
          [newLead.assigned_to]
        );
        if (assigneeResult.rows.length > 0) {
          assigneeInfo = assigneeResult.rows[0];
        }
      }

      // Log lead creation
      await logLeadOperation(
        'CREATE' as any,
        authResult.user.id,
        newLead,
        undefined,
        context
      );

      return createResponse({
        id: newLead.id,
        firstName: newLead.first_name,
        lastName: newLead.last_name,
        email: newLead.email,
        phone: newLead.phone,
        source: newLead.source,
        status: newLead.status,
        assignedTo: newLead.assigned_to,
        assignedToName: assigneeInfo?.name,
        assignedToEmail: assigneeInfo?.email,
        createdAt: newLead.created_at,
        updatedAt: newLead.updated_at
      }, true, 'Lead created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating lead:', dbError);
      return createErrorResponse('Failed to create lead', 500);
    }

  } catch (error) {
    console.error('Create lead error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
