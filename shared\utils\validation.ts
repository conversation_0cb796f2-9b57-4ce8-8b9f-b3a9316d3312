/**
 * Validation utilities for the Innovative Centre Platform
 */

import { ValidationPatterns, UserRole, PaymentType, PaymentMethod } from '../types/common';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Email validation
export function validateEmail(email: string): ValidationResult {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email is required');
  } else if (!ValidationPatterns.EMAIL.test(email)) {
    errors.push('Invalid email format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Password validation
export function validatePassword(password: string): ValidationResult {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
  } else {
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/(?=.*[a-z])/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/(?=.*\d)/.test(password)) {
      errors.push('Password must contain at least one number');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Phone validation
export function validatePhone(phone: string): ValidationResult {
  const errors: string[] = [];
  
  if (!phone) {
    errors.push('Phone number is required');
  } else if (!ValidationPatterns.PHONE.test(phone)) {
    errors.push('Invalid phone number format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// UUID validation
export function validateUUID(uuid: string): ValidationResult {
  const errors: string[] = [];
  
  if (!uuid) {
    errors.push('ID is required');
  } else if (!ValidationPatterns.UUID.test(uuid)) {
    errors.push('Invalid ID format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// User role validation
export function validateUserRole(role: string): ValidationResult {
  const errors: string[] = [];
  
  if (!role) {
    errors.push('User role is required');
  } else if (!Object.values(UserRole).includes(role as UserRole)) {
    errors.push('Invalid user role');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Amount validation
export function validateAmount(amount: number): ValidationResult {
  const errors: string[] = [];
  
  if (amount === undefined || amount === null) {
    errors.push('Amount is required');
  } else if (amount < 0) {
    errors.push('Amount must be positive');
  } else if (amount > 999999.99) {
    errors.push('Amount is too large');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Date validation
export function validateDate(date: string | Date): ValidationResult {
  const errors: string[] = [];
  
  if (!date) {
    errors.push('Date is required');
  } else {
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      errors.push('Invalid date format');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Time validation (HH:MM format)
export function validateTime(time: string): ValidationResult {
  const errors: string[] = [];
  
  if (!time) {
    errors.push('Time is required');
  } else if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time)) {
    errors.push('Invalid time format (use HH:MM)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Payment type validation
export function validatePaymentType(type: string): ValidationResult {
  const errors: string[] = [];
  
  if (!type) {
    errors.push('Payment type is required');
  } else if (!Object.values(PaymentType).includes(type as PaymentType)) {
    errors.push('Invalid payment type');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Payment method validation
export function validatePaymentMethod(method: string): ValidationResult {
  const errors: string[] = [];
  
  if (!method) {
    errors.push('Payment method is required');
  } else if (!Object.values(PaymentMethod).includes(method as PaymentMethod)) {
    errors.push('Invalid payment method');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Generic required field validation
export function validateRequired(value: any, fieldName: string): ValidationResult {
  const errors: string[] = [];
  
  if (value === undefined || value === null || value === '') {
    errors.push(`${fieldName} is required`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// String length validation
export function validateStringLength(
  value: string, 
  fieldName: string, 
  minLength?: number, 
  maxLength?: number
): ValidationResult {
  const errors: string[] = [];
  
  if (minLength && value.length < minLength) {
    errors.push(`${fieldName} must be at least ${minLength} characters long`);
  }
  
  if (maxLength && value.length > maxLength) {
    errors.push(`${fieldName} must be no more than ${maxLength} characters long`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Combine multiple validation results
export function combineValidationResults(...results: ValidationResult[]): ValidationResult {
  const allErrors = results.flatMap(result => result.errors);
  
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
}

// Validate object against schema
export function validateObject<T>(
  obj: any,
  validators: Record<keyof T, (value: any) => ValidationResult>
): ValidationResult {
  const results = Object.entries(validators).map(([key, validator]) => 
    validator(obj[key])
  );
  
  return combineValidationResults(...results);
}
