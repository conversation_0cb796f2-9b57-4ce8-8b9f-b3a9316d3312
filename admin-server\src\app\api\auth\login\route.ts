/**
 * Authentication Login API endpoint
 * Handles user login with email and password
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { verifyPassword, generateTokens } from '@/lib/auth';
import { query } from '@/lib/db';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { email, password } = body;

    // Get request context for activity logging
    const context = getRequestContext(request.headers);

    try {
      // Find user by email
      const userResult = await query<User>(
        'SELECT id, email, password_hash, role, name, is_active FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (userResult.rows.length === 0) {
        // Log failed login attempt
        console.log(`Failed login attempt for email: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      const user = userResult.rows[0];

      // Check if user is active
      if (!user.is_active) {
        console.log(`Login attempt for inactive user: ${email}`);
        return createErrorResponse('Account is deactivated', 401);
      }

      // Verify password
      const isPasswordValid = await verifyPassword(password, user.password_hash);
      if (!isPasswordValid) {
        console.log(`Invalid password for user: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Generate JWT tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        role: user.role
      });

      // Log successful login
      await logAuthEvent('LOGIN', user.id, context);

      // Return success response
      return createResponse({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name
        },
        token: tokens.token,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      }, true, 'Login successful');

    } catch (dbError) {
      console.error('Database error during login:', dbError);
      
      // For development, we can return a mock response if database is not available
      if (process.env.NODE_ENV === 'development' && email === '<EMAIL>') {
        console.log('Using mock authentication for development');
        
        const mockTokens = generateTokens({
          id: 'mock-admin-id',
          email: email,
          role: UserRole.ADMIN
        });

        return createResponse({
          user: {
            id: 'mock-admin-id',
            email: email,
            role: UserRole.ADMIN,
            name: 'Mock Admin User'
          },
          token: mockTokens.token,
          refreshToken: mockTokens.refreshToken,
          expiresIn: mockTokens.expiresIn
        }, true, 'Login successful (development mode)');
      }
      
      return createErrorResponse('Authentication service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Login error:', error);
    return createErrorResponse(
      'Login failed due to server error',
      500
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
