/**
 * Individual lead management endpoints
 * Handles GET, PUT, DELETE operations for specific leads
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidEmail,
  isValidUUID,
  formatPhoneNumber
} from '@/lib/utils';
import { logLeadOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole, LeadStatus } from '@/shared/types/common';

interface Lead {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  source?: string;
  status: LeadStatus;
  assigned_to?: string;
  created_at: Date;
  updated_at: Date;
}

interface LeadWithAssignee extends Lead {
  assigned_to_name?: string;
  assigned_to_email?: string;
}

// GET /api/leads/[id] - Get specific lead
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid lead ID format', 400);
    }

    // Check permissions - management and reception can view leads
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get lead details with assignee information
      const leadResult = await query<LeadWithAssignee>(
        `SELECT l.id, l.first_name, l.last_name, l.email, l.phone, l.source, 
                l.status, l.assigned_to, l.created_at, l.updated_at,
                u.name as assigned_to_name, u.email as assigned_to_email
         FROM leads l
         LEFT JOIN users u ON l.assigned_to = u.id
         WHERE l.id = $1`,
        [id]
      );

      if (leadResult.rows.length === 0) {
        return createErrorResponse('Lead not found', 404);
      }

      const lead = leadResult.rows[0];

      return createResponse({
        id: lead.id,
        firstName: lead.first_name,
        lastName: lead.last_name,
        email: lead.email,
        phone: lead.phone,
        source: lead.source,
        status: lead.status,
        assignedTo: lead.assigned_to,
        assignedToName: lead.assigned_to_name,
        assignedToEmail: lead.assigned_to_email,
        createdAt: lead.created_at,
        updatedAt: lead.updated_at
      }, true, 'Lead retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving lead:', dbError);
      return createErrorResponse('Failed to retrieve lead', 500);
    }

  } catch (error) {
    console.error('Get lead error:', error);
    return createErrorResponse('Failed to retrieve lead', 500);
  }
}

// PUT /api/leads/[id] - Update specific lead
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid lead ID format', 400);
    }

    // Check permissions - management and reception can update leads
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { firstName, lastName, email, phone, source, status, assignedTo } = body;

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate status if provided
    if (status && !['new', 'contacted', 'interested', 'enrolled', 'rejected'].includes(status)) {
      return createErrorResponse('Invalid status. Must be new, contacted, interested, enrolled, or rejected', 400);
    }

    // Validate assignedTo if provided
    if (assignedTo && !isValidUUID(assignedTo)) {
      return createErrorResponse('Invalid assigned user ID format', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current lead data
      const currentLeadResult = await query<Lead>(
        `SELECT id, first_name, last_name, email, phone, source, 
                status, assigned_to, created_at, updated_at 
         FROM leads WHERE id = $1`,
        [id]
      );

      if (currentLeadResult.rows.length === 0) {
        return createErrorResponse('Lead not found', 404);
      }

      const currentLead = currentLeadResult.rows[0];

      // Check if assigned user exists and is reception or management
      if (assignedTo) {
        const assigneeResult = await query(
          'SELECT id, role FROM users WHERE id = $1 AND is_active = true',
          [assignedTo]
        );

        if (assigneeResult.rows.length === 0) {
          return createErrorResponse('Assigned user not found or inactive', 400);
        }

        const assignee = assigneeResult.rows[0];
        if (!['management', 'reception'].includes(assignee.role)) {
          return createErrorResponse('Can only assign leads to management or reception staff', 400);
        }
      }

      // Check if email is being changed and if it already exists
      if (email && email.toLowerCase() !== currentLead.email) {
        const existingLeadResult = await query(
          'SELECT id FROM leads WHERE email = $1 AND id != $2',
          [email.toLowerCase(), id]
        );

        if (existingLeadResult.rows.length > 0) {
          return createErrorResponse('Lead with this email already exists', 409);
        }
      }

      // Build update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (firstName !== undefined) {
        updateFields.push(`first_name = $${paramIndex++}`);
        updateValues.push(firstName.trim());
      }

      if (lastName !== undefined) {
        updateFields.push(`last_name = $${paramIndex++}`);
        updateValues.push(lastName.trim());
      }

      if (email !== undefined) {
        updateFields.push(`email = $${paramIndex++}`);
        updateValues.push(email ? email.toLowerCase() : null);
      }

      if (phone !== undefined) {
        updateFields.push(`phone = $${paramIndex++}`);
        updateValues.push(phone ? formatPhoneNumber(phone) : null);
      }

      if (source !== undefined) {
        updateFields.push(`source = $${paramIndex++}`);
        updateValues.push(source?.trim() || null);
      }

      if (status !== undefined) {
        updateFields.push(`status = $${paramIndex++}`);
        updateValues.push(status);
      }

      if (assignedTo !== undefined) {
        updateFields.push(`assigned_to = $${paramIndex++}`);
        updateValues.push(assignedTo || null);
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const updateSql = `
        UPDATE leads 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, first_name, last_name, email, phone, source, 
                  status, assigned_to, created_at, updated_at
      `;

      const updatedLeadResult = await query<Lead>(updateSql, updateValues);
      const updatedLead = updatedLeadResult.rows[0];

      // Get assignee information if assigned
      let assigneeInfo = null;
      if (updatedLead.assigned_to) {
        const assigneeResult = await query(
          'SELECT name, email FROM users WHERE id = $1',
          [updatedLead.assigned_to]
        );
        if (assigneeResult.rows.length > 0) {
          assigneeInfo = assigneeResult.rows[0];
        }
      }

      // Log lead update
      await logLeadOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedLead,
        currentLead,
        context
      );

      return createResponse({
        id: updatedLead.id,
        firstName: updatedLead.first_name,
        lastName: updatedLead.last_name,
        email: updatedLead.email,
        phone: updatedLead.phone,
        source: updatedLead.source,
        status: updatedLead.status,
        assignedTo: updatedLead.assigned_to,
        assignedToName: assigneeInfo?.name,
        assignedToEmail: assigneeInfo?.email,
        createdAt: updatedLead.created_at,
        updatedAt: updatedLead.updated_at
      }, true, 'Lead updated successfully');

    } catch (dbError) {
      console.error('Database error updating lead:', dbError);
      return createErrorResponse('Failed to update lead', 500);
    }

  } catch (error) {
    console.error('Update lead error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

// DELETE /api/leads/[id] - Delete specific lead
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid lead ID format', 400);
    }

    // Only management can delete leads
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current lead data
      const currentLeadResult = await query<Lead>(
        `SELECT id, first_name, last_name, email, phone, source, 
                status, assigned_to, created_at, updated_at 
         FROM leads WHERE id = $1`,
        [id]
      );

      if (currentLeadResult.rows.length === 0) {
        return createErrorResponse('Lead not found', 404);
      }

      const currentLead = currentLeadResult.rows[0];

      // Delete lead (hard delete)
      await query('DELETE FROM leads WHERE id = $1', [id]);

      // Log lead deletion
      await logLeadOperation(
        'DELETE' as any,
        authResult.user.id,
        currentLead,
        currentLead,
        context
      );

      return createResponse(
        { message: 'Lead deleted successfully' },
        true,
        'Lead deleted successfully'
      );

    } catch (dbError) {
      console.error('Database error deleting lead:', dbError);
      return createErrorResponse('Failed to delete lead', 500);
    }

  } catch (error) {
    console.error('Delete lead error:', error);
    return createErrorResponse('Failed to delete lead', 500);
  }
}
