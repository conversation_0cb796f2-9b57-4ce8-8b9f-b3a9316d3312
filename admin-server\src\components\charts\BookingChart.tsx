/**
 * Booking Chart Component
 * Displays booking trends and statistics using Recharts
 */

'use client';

import React from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';

interface BookingData {
  date: string;
  bookings: number;
  confirmed: number;
}

interface BookingChartProps {
  data: BookingData[];
  type?: 'area' | 'bar';
  height?: number;
  showConfirmed?: boolean;
}

export default function BookingChart({ 
  data, 
  type = 'area', 
  height = 300,
  showConfirmed = true 
}: BookingChartProps) {
  // Format data for display
  const formattedData = data.map(item => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }),
    bookings: item.bookings,
    confirmed: item.confirmed,
    pending: item.bookings - item.confirmed
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'bookings' && 'Total Bookings: '}
              {entry.dataKey === 'confirmed' && 'Confirmed: '}
              {entry.dataKey === 'pending' && 'Pending: '}
              {entry.value} {entry.value === 1 ? 'booking' : 'bookings'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (type === 'bar') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={formattedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: 12 }}
            stroke="#6b7280"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            stroke="#6b7280"
            allowDecimals={false}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar 
            dataKey="confirmed" 
            stackId="a"
            fill="#10b981" 
            radius={[0, 0, 0, 0]}
            name="Confirmed"
          />
          <Bar 
            dataKey="pending" 
            stackId="a"
            fill="#f59e0b" 
            radius={[4, 4, 0, 0]}
            name="Pending"
          />
        </BarChart>
      </ResponsiveContainer>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <AreaChart data={formattedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <defs>
          <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
          </linearGradient>
          <linearGradient id="colorConfirmed" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
            <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tick={{ fontSize: 12 }}
          stroke="#6b7280"
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          stroke="#6b7280"
          allowDecimals={false}
        />
        <Tooltip content={<CustomTooltip />} />
        <Area 
          type="monotone" 
          dataKey="bookings" 
          stroke="#3b82f6" 
          fillOpacity={1} 
          fill="url(#colorBookings)"
          strokeWidth={2}
          name="Total Bookings"
        />
        {showConfirmed && (
          <Area 
            type="monotone" 
            dataKey="confirmed" 
            stroke="#10b981" 
            fillOpacity={1} 
            fill="url(#colorConfirmed)"
            strokeWidth={2}
            name="Confirmed"
          />
        )}
      </AreaChart>
    </ResponsiveContainer>
  );
}
