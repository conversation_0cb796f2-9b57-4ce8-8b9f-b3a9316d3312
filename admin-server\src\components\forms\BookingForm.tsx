/**
 * Booking Form Component
 * Form for creating and editing cabinet bookings
 */

'use client';

import React, { useState, useEffect } from 'react';

interface BookingFormData {
  cabinetId: string;
  date: string;
  startTime: string;
  endTime: string;
  purpose: string;
}

interface Cabinet {
  id: string;
  name: string;
  capacity: number;
  equipment: string[];
  hourlyRate?: number;
  isAvailable: boolean;
}

interface BookingFormProps {
  onSubmit: (data: BookingFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  initialData?: Partial<BookingFormData>;
  mode?: 'create' | 'edit';
  cabinets?: Cabinet[];
  selectedCabinet?: Cabinet;
}

// Common time slots
const TIME_SLOTS = [
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',
  '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',
  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',
  '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
  '20:00', '20:30', '21:00', '21:30', '22:00'
];

export default function BookingForm({
  onSubmit,
  onCancel,
  isLoading = false,
  initialData = {},
  mode = 'create',
  cabinets = [],
  selectedCabinet
}: BookingFormProps) {
  const [formData, setFormData] = useState<BookingFormData>({
    cabinetId: initialData.cabinetId || selectedCabinet?.id || '',
    date: initialData.date || (() => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString().split('T')[0];
    })(),
    startTime: initialData.startTime || '09:00',
    endTime: initialData.endTime || '10:00',
    purpose: initialData.purpose || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedCabinetInfo, setSelectedCabinetInfo] = useState<Cabinet | null>(selectedCabinet || null);

  // Update selected cabinet info when cabinet changes
  useEffect(() => {
    if (formData.cabinetId) {
      const cabinet = cabinets.find(c => c.id === formData.cabinetId);
      setSelectedCabinetInfo(cabinet || null);
    } else {
      setSelectedCabinetInfo(null);
    }
  }, [formData.cabinetId, cabinets]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate cabinet selection
    if (!formData.cabinetId) {
      newErrors.cabinetId = 'Please select a cabinet';
    }

    // Validate date
    if (!formData.date) {
      newErrors.date = 'Date is required';
    } else {
      const bookingDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (bookingDate < today) {
        newErrors.date = 'Cannot book for past dates';
      }

      // Check advance booking limit (e.g., 90 days)
      const maxAdvanceDate = new Date();
      maxAdvanceDate.setDate(maxAdvanceDate.getDate() + 90);
      
      if (bookingDate > maxAdvanceDate) {
        newErrors.date = 'Cannot book more than 90 days in advance';
      }
    }

    // Validate times
    if (!formData.startTime) {
      newErrors.startTime = 'Start time is required';
    }

    if (!formData.endTime) {
      newErrors.endTime = 'End time is required';
    }

    if (formData.startTime && formData.endTime) {
      if (formData.startTime >= formData.endTime) {
        newErrors.endTime = 'End time must be after start time';
      }

      // Calculate duration
      const [startHour, startMin] = formData.startTime.split(':').map(Number);
      const [endHour, endMin] = formData.endTime.split(':').map(Number);
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;
      const durationMinutes = endMinutes - startMinutes;

      if (durationMinutes < 30) {
        newErrors.endTime = 'Minimum booking duration is 30 minutes';
      }

      if (durationMinutes > 480) { // 8 hours
        newErrors.endTime = 'Maximum booking duration is 8 hours';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting booking:', error);
    }
  };

  const handleInputChange = (field: keyof BookingFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Auto-adjust end time when start time changes
    if (field === 'startTime' && value) {
      const [hour, min] = value.split(':').map(Number);
      const endHour = hour + 1;
      const endTime = `${endHour.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`;
      
      if (endHour < 24) {
        setFormData(prev => ({ ...prev, endTime }));
      }
    }
  };

  const calculateDuration = (): string => {
    if (!formData.startTime || !formData.endTime) return '';
    
    const [startHour, startMin] = formData.startTime.split(':').map(Number);
    const [endHour, endMin] = formData.endTime.split(':').map(Number);
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    const durationMinutes = endMinutes - startMinutes;
    
    if (durationMinutes <= 0) return '';
    
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    
    if (hours > 0) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  const calculateCost = (): string => {
    if (!selectedCabinetInfo?.hourlyRate || !formData.startTime || !formData.endTime) return '';
    
    const [startHour, startMin] = formData.startTime.split(':').map(Number);
    const [endHour, endMin] = formData.endTime.split(':').map(Number);
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    const durationMinutes = endMinutes - startMinutes;
    
    if (durationMinutes <= 0) return '';
    
    const durationHours = durationMinutes / 60;
    const cost = durationHours * selectedCabinetInfo.hourlyRate;
    
    return `$${cost.toFixed(2)}`;
  };

  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 90);
    return maxDate.toISOString().split('T')[0];
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">
          {mode === 'create' ? 'Book Cabinet' : 'Edit Booking'}
        </h3>
        <p className="mt-1 text-sm text-gray-600">
          {mode === 'create' 
            ? 'Reserve a cabinet for your meeting or event.'
            : 'Update your booking details.'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Cabinet Selection */}
        {mode === 'create' && !selectedCabinet && (
          <div>
            <label htmlFor="cabinetId" className="block text-sm font-medium text-gray-700 mb-1">
              Select Cabinet *
            </label>
            <select
              id="cabinetId"
              value={formData.cabinetId}
              onChange={(e) => handleInputChange('cabinetId', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.cabinetId ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={isLoading}
            >
              <option value="">Choose a cabinet...</option>
              {cabinets.filter(c => c.isAvailable).map((cabinet) => (
                <option key={cabinet.id} value={cabinet.id}>
                  {cabinet.name} (Capacity: {cabinet.capacity})
                  {cabinet.hourlyRate && ` - $${cabinet.hourlyRate}/hour`}
                </option>
              ))}
            </select>
            {errors.cabinetId && (
              <p className="mt-1 text-sm text-red-600">{errors.cabinetId}</p>
            )}
          </div>
        )}

        {/* Selected Cabinet Info */}
        {selectedCabinetInfo && (
          <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Selected Cabinet</h4>
            <div className="space-y-1 text-sm text-blue-800">
              <div><strong>Name:</strong> {selectedCabinetInfo.name}</div>
              <div><strong>Capacity:</strong> {selectedCabinetInfo.capacity} people</div>
              {selectedCabinetInfo.hourlyRate && (
                <div><strong>Rate:</strong> ${selectedCabinetInfo.hourlyRate}/hour</div>
              )}
              {selectedCabinetInfo.equipment.length > 0 && (
                <div>
                  <strong>Equipment:</strong> {selectedCabinetInfo.equipment.join(', ')}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Date */}
        <div>
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
            Date *
          </label>
          <input
            type="date"
            id="date"
            value={formData.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            min={getMinDate()}
            max={getMaxDate()}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.date ? 'border-red-500' : 'border-gray-300'
            }`}
            disabled={isLoading}
          />
          {errors.date && (
            <p className="mt-1 text-sm text-red-600">{errors.date}</p>
          )}
        </div>

        {/* Time Selection */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="startTime" className="block text-sm font-medium text-gray-700 mb-1">
              Start Time *
            </label>
            <select
              id="startTime"
              value={formData.startTime}
              onChange={(e) => handleInputChange('startTime', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.startTime ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={isLoading}
            >
              {TIME_SLOTS.map((time) => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
            {errors.startTime && (
              <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
            )}
          </div>

          <div>
            <label htmlFor="endTime" className="block text-sm font-medium text-gray-700 mb-1">
              End Time *
            </label>
            <select
              id="endTime"
              value={formData.endTime}
              onChange={(e) => handleInputChange('endTime', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.endTime ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={isLoading}
            >
              {TIME_SLOTS.map((time) => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
            {errors.endTime && (
              <p className="mt-1 text-sm text-red-600">{errors.endTime}</p>
            )}
          </div>
        </div>

        {/* Duration and Cost Display */}
        {(calculateDuration() || calculateCost()) && (
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex justify-between text-sm">
              {calculateDuration() && (
                <div>
                  <span className="font-medium text-gray-700">Duration:</span>
                  <span className="ml-2 text-gray-900">{calculateDuration()}</span>
                </div>
              )}
              {calculateCost() && (
                <div>
                  <span className="font-medium text-gray-700">Estimated Cost:</span>
                  <span className="ml-2 text-gray-900 font-semibold">{calculateCost()}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Purpose */}
        <div>
          <label htmlFor="purpose" className="block text-sm font-medium text-gray-700 mb-1">
            Purpose
          </label>
          <textarea
            id="purpose"
            rows={3}
            value={formData.purpose}
            onChange={(e) => handleInputChange('purpose', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe the purpose of your booking (optional)"
            disabled={isLoading}
          />
          <p className="mt-1 text-xs text-gray-500">
            Optional. Helps with scheduling and resource planning.
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {mode === 'create' ? 'Booking...' : 'Updating...'}
              </div>
            ) : (
              mode === 'create' ? 'Book Cabinet' : 'Update Booking'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
