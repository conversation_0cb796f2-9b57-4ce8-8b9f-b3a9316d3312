/**
 * Invoice Statistics API endpoint
 * Provides statistical information about invoices and billing data
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface InvoiceStats {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  averageInvoiceAmount: number;
  statusBreakdown: Record<string, number>;
  overdueInvoices: Array<{
    id: string;
    studentId: string;
    amount: number;
    dueDate: Date;
    daysPastDue: number;
  }>;
  recentInvoices: Array<{
    id: string;
    studentId: string;
    amount: number;
    dueDate: Date;
    status: string;
    createdAt: Date;
    createdByName: string;
  }>;
  monthlyInvoicing: Array<{
    month: string;
    invoiceCount: number;
    totalAmount: number;
    paidAmount: number;
  }>;
}

// GET /api/invoices/stats - Get invoice statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view invoice stats)
    if (!hasPermission(authResult.user.role, 'reports', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build date filter
    const dateConditions: string[] = [];
    const dateParams: any[] = [];
    let paramIndex = 1;

    if (dateFrom) {
      dateConditions.push(`created_at >= $${paramIndex}`);
      dateParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      dateConditions.push(`created_at <= $${paramIndex}`);
      dateParams.push(dateTo + ' 23:59:59');
      paramIndex++;
    }

    const dateFilter = dateConditions.length > 0 ? `WHERE ${dateConditions.join(' AND ')}` : '';

    // Get total invoice statistics
    const totalSql = `
      SELECT 
        COUNT(*) as total_invoices,
        COALESCE(SUM(amount), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as paid_amount,
        COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
        COALESCE(SUM(CASE WHEN status = 'pending' AND due_date < CURRENT_DATE THEN amount ELSE 0 END), 0) as overdue_amount,
        COALESCE(AVG(amount), 0) as average_amount
      FROM invoices 
      ${dateFilter}
    `;

    const totalResult = await query(totalSql, dateParams);
    const totals = totalResult.rows[0];

    // Get status breakdown
    const statusSql = `
      SELECT 
        status,
        COUNT(*) as count
      FROM invoices 
      ${dateFilter}
      GROUP BY status
      ORDER BY count DESC
    `;

    const statusResult = await query(statusSql, dateParams);
    const statusBreakdown: Record<string, number> = {};
    statusResult.rows.forEach(row => {
      statusBreakdown[row.status] = parseInt(row.count);
    });

    // Get overdue invoices
    const overdueSql = `
      SELECT 
        id,
        student_id,
        amount,
        due_date,
        CURRENT_DATE - due_date as days_past_due
      FROM invoices 
      WHERE status = 'pending' AND due_date < CURRENT_DATE
      ORDER BY days_past_due DESC
      LIMIT 20
    `;

    const overdueResult = await query(overdueSql);
    const overdueInvoices = overdueResult.rows.map(row => ({
      id: row.id,
      studentId: row.student_id,
      amount: parseFloat(row.amount),
      dueDate: row.due_date,
      daysPastDue: parseInt(row.days_past_due)
    }));

    // Get recent invoices
    const recentSql = `
      SELECT 
        i.id,
        i.student_id,
        i.amount,
        i.due_date,
        i.status,
        i.created_at,
        u.name as created_by_name
      FROM invoices i
      LEFT JOIN users u ON i.created_by = u.id
      ORDER BY i.created_at DESC
      LIMIT 10
    `;

    const recentResult = await query(recentSql);
    const recentInvoices = recentResult.rows.map(row => ({
      id: row.id,
      studentId: row.student_id,
      amount: parseFloat(row.amount),
      dueDate: row.due_date,
      status: row.status,
      createdAt: row.created_at,
      createdByName: row.created_by_name
    }));

    // Get monthly invoicing data (last 12 months or filtered period)
    const monthlySql = `
      SELECT 
        TO_CHAR(DATE_TRUNC('month', created_at), 'YYYY-MM') as month,
        COUNT(*) as invoice_count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as paid_amount
      FROM invoices 
      WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
        ${dateFrom ? `AND created_at >= $${dateParams.length > 0 ? 1 : 1}` : ''}
        ${dateTo ? `AND created_at <= $${dateParams.length > 1 ? 2 : dateParams.length + 1}` : ''}
      GROUP BY DATE_TRUNC('month', created_at)
      ORDER BY month DESC
      LIMIT 12
    `;

    const monthlyResult = await query(monthlySql, dateParams);
    const monthlyInvoicing = monthlyResult.rows.map(row => ({
      month: row.month,
      invoiceCount: parseInt(row.invoice_count),
      totalAmount: parseFloat(row.total_amount),
      paidAmount: parseFloat(row.paid_amount)
    }));

    const stats: InvoiceStats = {
      totalInvoices: parseInt(totals.total_invoices),
      totalAmount: parseFloat(totals.total_amount),
      paidAmount: parseFloat(totals.paid_amount),
      pendingAmount: parseFloat(totals.pending_amount),
      overdueAmount: parseFloat(totals.overdue_amount),
      averageInvoiceAmount: parseFloat(totals.average_amount),
      statusBreakdown,
      overdueInvoices,
      recentInvoices,
      monthlyInvoicing
    };

    return createResponse(stats, true, 'Invoice statistics retrieved successfully');

  } catch (error) {
    console.error('Error fetching invoice statistics:', error);
    return createErrorResponse('Failed to fetch invoice statistics', 500);
  }
}
