/**
 * User-specific Activity Logs API endpoint
 * Handles activity log retrieval for a specific user
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { ActivityAction, ResourceType } from '@/types';

interface ActivityLogWithUser {
  id: string;
  user_id: string;
  action: ActivityAction;
  resource_type: ResourceType;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  description?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
}

// GET /api/activity-logs/user/[userId] - Get activity logs for specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view activity logs)
    if (!hasPermission(authResult.user.role, 'activity-logs', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { userId } = params;
    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);

    // Additional filters
    const action = searchParams.get('action');
    const resourceType = searchParams.get('resourceType');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    try {
      // Build query with filters
      let whereClause = 'WHERE al.user_id = $1';
      const queryParams: any[] = [userId];
      let paramIndex = 2;

      if (action) {
        whereClause += ` AND al.action = $${paramIndex}`;
        queryParams.push(action);
        paramIndex++;
      }

      if (resourceType) {
        whereClause += ` AND al.resource_type = $${paramIndex}`;
        queryParams.push(resourceType);
        paramIndex++;
      }

      if (dateFrom) {
        whereClause += ` AND al.timestamp >= $${paramIndex}`;
        queryParams.push(dateFrom);
        paramIndex++;
      }

      if (dateTo) {
        whereClause += ` AND al.timestamp <= $${paramIndex}`;
        queryParams.push(dateTo);
        paramIndex++;
      }

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total 
         FROM activity_logs al 
         ${whereClause}`,
        queryParams
      );
      const total = parseInt(countResult.rows[0].total);

      // Get activity logs
      const offset = (pagination.page - 1) * pagination.limit;
      const logsResult = await query<ActivityLogWithUser>(
        `SELECT 
           al.id, al.user_id, al.action, al.resource_type, al.resource_id,
           al.old_values, al.new_values, al.ip_address, al.user_agent,
           al.timestamp, al.description,
           u.name as user_name, u.email as user_email, u.role as user_role
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ${whereClause} 
         ORDER BY al.timestamp DESC 
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...queryParams, pagination.limit, offset]
      );

      // Format the response
      const formattedLogs = logsResult.rows.map(log => ({
        id: log.id,
        userId: log.user_id,
        action: log.action,
        resourceType: log.resource_type,
        resourceId: log.resource_id,
        oldValues: log.old_values,
        newValues: log.new_values,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.timestamp,
        description: log.description,
        user: log.user_name ? {
          name: log.user_name,
          email: log.user_email,
          role: log.user_role
        } : null
      }));

      return createResponse({
        userId: userId,
        logs: formattedLogs,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, `Activity logs for user ${userId} retrieved successfully`);

    } catch (dbError) {
      console.error('Database error getting user activity logs:', dbError);
      
      // For development, return mock data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockLogs = [
          {
            id: 'mock-log-1',
            userId: userId,
            action: 'LOGIN' as ActivityAction,
            resourceType: 'USER' as ResourceType,
            resourceId: userId,
            timestamp: new Date(),
            description: 'User logged in',
            user: {
              name: 'Mock User',
              email: '<EMAIL>',
              role: 'admin'
            }
          }
        ];

        return createResponse({
          userId: userId,
          logs: mockLogs,
          pagination: {
            page: 1,
            limit: 20,
            total: mockLogs.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }, true, `Activity logs for user ${userId} retrieved successfully (development mode)`);
      }
      
      return createErrorResponse('Activity log service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get user activity logs error:', error);
    return createErrorResponse('Failed to get user activity logs', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
