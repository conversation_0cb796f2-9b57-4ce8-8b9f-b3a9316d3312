/**
 * Change Password API endpoint
 * Allows users to change their own password
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, verifyPassword, hashPassword } from '@/lib/auth';
import { query } from '@/lib/db';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: string;
  name: string;
  is_active: boolean;
}

// POST /api/users/change-password - Change user's own password
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    const body: ChangePasswordRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['currentPassword', 'newPassword']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { currentPassword, newPassword } = body;
    const context = getRequestContext(request.headers);

    // Validate new password strength
    if (newPassword.length < 8) {
      return createErrorResponse('New password must be at least 8 characters long', 400);
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      return createErrorResponse(
        'New password must contain at least one uppercase letter, one lowercase letter, and one number',
        400
      );
    }

    try {
      // Get current user with password hash
      const userResult = await query<User>(
        'SELECT id, email, password_hash, role, name, is_active FROM users WHERE id = $1',
        [authResult.user.id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      // Check if user is active
      if (!user.is_active) {
        return createErrorResponse('Account is deactivated', 401);
      }

      // Verify current password
      const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password_hash);
      if (!isCurrentPasswordValid) {
        return createErrorResponse('Current password is incorrect', 400);
      }

      // Check if new password is different from current
      const isSamePassword = await verifyPassword(newPassword, user.password_hash);
      if (isSamePassword) {
        return createErrorResponse('New password must be different from current password', 400);
      }

      // Hash new password
      const newPasswordHash = await hashPassword(newPassword);

      // Update password
      await query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [newPasswordHash, user.id]
      );

      // Log password change (without sensitive data)
      await logUserOperation(
        'UPDATE' as any,
        user.id,
        { id: user.id, email: user.email, action: 'password_change' },
        undefined,
        context
      );

      return createResponse(
        { message: 'Password changed successfully' },
        true,
        'Password updated successfully'
      );

    } catch (dbError) {
      console.error('Database error changing password:', dbError);
      
      // For development, return success if database is not available
      if (process.env.NODE_ENV === 'development') {
        return createResponse(
          { message: 'Password changed successfully' },
          true,
          'Password updated successfully (development mode)'
        );
      }
      
      return createErrorResponse('Password change service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Change password error:', error);
    return createErrorResponse('Failed to change password', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
