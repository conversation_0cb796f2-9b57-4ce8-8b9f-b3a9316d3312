/**
 * Payments API endpoint
 * Handles payment management operations (list, create)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logPaymentOperation, getRequestContext } from '@/lib/activity-logger';
import { PaymentType, PaymentMethod, PaymentStatus, ActivityAction } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface Payment {
  id: string;
  student_id: string;
  amount: number;
  payment_type: PaymentType;
  payment_method: PaymentMethod;
  description?: string;
  status: PaymentStatus;
  processed_by: string;
  payment_date: Date;
  created_at: Date;
  updated_at: Date;
}

interface PaymentWithUser extends Payment {
  processed_by_name?: string;
  processed_by_email?: string;
}

interface CreatePaymentRequest {
  studentId: string;
  amount: number;
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  description?: string;
  paymentDate?: string;
}

// GET /api/payments - List payments with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    // Build query conditions
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Filter by student ID
    if (filters.studentId) {
      conditions.push(`p.student_id = $${paramIndex}`);
      params.push(filters.studentId);
      paramIndex++;
    }

    // Filter by payment type
    if (filters.paymentType) {
      conditions.push(`p.payment_type = $${paramIndex}`);
      params.push(filters.paymentType);
      paramIndex++;
    }

    // Filter by payment method
    if (filters.paymentMethod) {
      conditions.push(`p.payment_method = $${paramIndex}`);
      params.push(filters.paymentMethod);
      paramIndex++;
    }

    // Filter by status
    if (filters.status) {
      conditions.push(`p.status = $${paramIndex}`);
      params.push(filters.status);
      paramIndex++;
    }

    // Filter by date range
    if (filters.dateFrom) {
      conditions.push(`p.payment_date >= $${paramIndex}`);
      params.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters.dateTo) {
      conditions.push(`p.payment_date <= $${paramIndex}`);
      params.push(filters.dateTo + ' 23:59:59');
      paramIndex++;
    }

    // Filter by amount range
    if (filters.amountFrom) {
      conditions.push(`p.amount >= $${paramIndex}`);
      params.push(parseFloat(filters.amountFrom));
      paramIndex++;
    }

    if (filters.amountTo) {
      conditions.push(`p.amount <= $${paramIndex}`);
      params.push(parseFloat(filters.amountTo));
      paramIndex++;
    }

    // Search in description
    if (filters.search) {
      conditions.push(`p.description ILIKE $${paramIndex}`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    let orderBy = 'p.payment_date DESC';
    if (pagination.sortBy) {
      const sortColumn = pagination.sortBy === 'amount' ? 'p.amount' :
                        pagination.sortBy === 'paymentDate' ? 'p.payment_date' :
                        pagination.sortBy === 'status' ? 'p.status' :
                        'p.payment_date';
      orderBy = `${sortColumn} ${pagination.sortOrder}`;
    }

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM payments p
      ${whereClause}
    `;
    
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const dataSql = `
      SELECT 
        p.id,
        p.student_id,
        p.amount,
        p.payment_type,
        p.payment_method,
        p.description,
        p.status,
        p.processed_by,
        p.payment_date,
        p.created_at,
        p.updated_at,
        u.name as processed_by_name,
        u.email as processed_by_email
      FROM payments p
      LEFT JOIN users u ON p.processed_by = u.id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(pagination.limit, offset);
    const dataResult = await query<PaymentWithUser>(dataSql, params);

    // Format response
    const payments = dataResult.rows.map(payment => ({
      id: payment.id,
      studentId: payment.student_id,
      amount: payment.amount,
      paymentType: payment.payment_type,
      paymentMethod: payment.payment_method,
      description: payment.description,
      status: payment.status,
      processedBy: payment.processed_by,
      paymentDate: payment.payment_date,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
      processedByUser: payment.processed_by_name ? {
        id: payment.processed_by,
        name: payment.processed_by_name,
        email: payment.processed_by_email
      } : undefined
    }));

    return createResponse({
      data: payments,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
        hasNext: pagination.page < Math.ceil(total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    }, true, 'Payments retrieved successfully');

  } catch (error) {
    console.error('Error fetching payments:', error);
    return createErrorResponse('Failed to fetch payments', 500);
  }
}

// POST /api/payments - Create a new payment
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body: CreatePaymentRequest = await request.json();

    // Validate required fields
    const validation = validateRequiredFields(body, [
      'studentId', 'amount', 'paymentType', 'paymentMethod'
    ]);

    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { studentId, amount, paymentType, paymentMethod, description, paymentDate } = body;

    // Validate amount
    if (amount < PAYMENT_CONFIG.MIN_AMOUNT || amount > PAYMENT_CONFIG.MAX_AMOUNT) {
      return createErrorResponse(
        `Amount must be between $${PAYMENT_CONFIG.MIN_AMOUNT} and $${PAYMENT_CONFIG.MAX_AMOUNT}`,
        400
      );
    }

    // Validate payment type
    if (!Object.values(PaymentType).includes(paymentType)) {
      return createErrorResponse('Invalid payment type', 400);
    }

    // Validate payment method
    if (!Object.values(PaymentMethod).includes(paymentMethod)) {
      return createErrorResponse('Invalid payment method', 400);
    }

    // Parse payment date or use current date
    const processedPaymentDate = paymentDate ? new Date(paymentDate) : new Date();

    // Validate payment date
    if (isNaN(processedPaymentDate.getTime())) {
      return createErrorResponse('Invalid payment date format', 400);
    }

    // Create payment record
    const sql = `
      INSERT INTO payments (
        student_id, amount, payment_type, payment_method,
        description, status, processed_by, payment_date
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, student_id, amount, payment_type, payment_method,
                description, status, processed_by, payment_date, created_at, updated_at
    `;

    const params = [
      studentId,
      amount,
      paymentType,
      paymentMethod,
      description || null,
      PaymentStatus.COMPLETED, // Default status
      authResult.user.id,
      processedPaymentDate
    ];

    const result = await query<Payment>(sql, params);
    const payment = result.rows[0];

    // Log the payment creation
    const context = getRequestContext(request.headers);
    await logPaymentOperation(
      ActivityAction.CREATE,
      authResult.user.id,
      {
        id: payment.id,
        studentId: payment.student_id,
        amount: payment.amount,
        paymentType: payment.payment_type,
        paymentMethod: payment.payment_method,
        description: payment.description,
        status: payment.status
      },
      undefined,
      context
    );

    // Format response
    const responsePayment = {
      id: payment.id,
      studentId: payment.student_id,
      amount: payment.amount,
      paymentType: payment.payment_type,
      paymentMethod: payment.payment_method,
      description: payment.description,
      status: payment.status,
      processedBy: payment.processed_by,
      paymentDate: payment.payment_date,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    };

    return createResponse(
      responsePayment,
      true,
      'Payment recorded successfully',
      undefined,
      201
    );

  } catch (error) {
    console.error('Error creating payment:', error);
    return createErrorResponse('Failed to create payment', 500);
  }
}
