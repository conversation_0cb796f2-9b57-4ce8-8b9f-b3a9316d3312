/**
 * Payments API Tests
 * Unit tests for payments API endpoints
 */

import { NextRequest } from 'next/server';
import { GET, POST } from '../route';
import { mockUser, mockPayment, mockApiResponse, mockErrorResponse } from '../../../../tests/utils/test-helpers';

// Mock dependencies
jest.mock('@/lib/auth', () => ({
  getUserFromRequest: jest.fn(),
  hasPermission: jest.fn(),
}));

jest.mock('@/lib/db', () => ({
  query: jest.fn(),
}));

jest.mock('@/lib/activity-logger', () => ({
  logPaymentOperation: jest.fn(),
  getRequestContext: jest.fn().mockReturnValue({}),
}));

import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logPaymentOperation } from '@/lib/activity-logger';

const mockGetUserFromRequest = getUserFromRequest as jest.MockedFunction<typeof getUserFromRequest>;
const mockHasPermission = hasPermission as jest.MockedFunction<typeof hasPermission>;
const mockQuery = query as jest.MockedFunction<typeof query>;
const mockLogPaymentOperation = logPaymentOperation as jest.MockedFunction<typeof logPaymentOperation>;

describe('/api/payments', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/payments', () => {
    it('should return payments list for authenticated user', async () => {
      // Setup mocks
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);
      
      const mockPayments = [mockPayment(), mockPayment({ id: 'payment-2' })];
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '2' }] }) // Count query
        .mockResolvedValueOnce({ rows: mockPayments }); // Data query

      // Create request
      const request = new NextRequest('http://localhost:3000/api/payments');
      
      // Execute
      const response = await GET(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.data).toHaveLength(2);
      expect(data.data.pagination).toBeDefined();
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: false,
        user: null,
      });

      const request = new NextRequest('http://localhost:3000/api/payments');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Not authenticated');
    });

    it('should return 403 for insufficient permissions', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser({ role: 'user' }),
      });
      mockHasPermission.mockReturnValue(false);

      const request = new NextRequest('http://localhost:3000/api/payments');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Insufficient permissions');
    });

    it('should handle pagination parameters', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);
      
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '10' }] })
        .mockResolvedValueOnce({ rows: [] });

      const request = new NextRequest('http://localhost:3000/api/payments?page=2&limit=5');
      await GET(request);

      // Check that query was called with correct LIMIT and OFFSET
      const queryCall = mockQuery.mock.calls[1];
      expect(queryCall[1]).toContain(5); // LIMIT
      expect(queryCall[1]).toContain(5); // OFFSET (page 2, limit 5)
    });

    it('should handle filter parameters', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);
      
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '1' }] })
        .mockResolvedValueOnce({ rows: [] });

      const request = new NextRequest('http://localhost:3000/api/payments?status=completed&studentId=STU001');
      await GET(request);

      // Check that query includes filter conditions
      const queryCall = mockQuery.mock.calls[1];
      expect(queryCall[0]).toContain('status = $');
      expect(queryCall[1]).toContain('completed');
      expect(queryCall[1]).toContain('STU001');
    });
  });

  describe('POST /api/payments', () => {
    const validPaymentData = {
      studentId: 'STU001',
      amount: 150.00,
      paymentType: 'tuition',
      paymentMethod: 'card',
      description: 'Monthly tuition payment',
      paymentDate: '2024-01-15',
    };

    it('should create payment for authenticated user', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);
      
      const createdPayment = mockPayment(validPaymentData);
      mockQuery.mockResolvedValue({ rows: [createdPayment] });

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.studentId).toBe(validPaymentData.studentId);
      expect(mockLogPaymentOperation).toHaveBeenCalled();
    });

    it('should return 400 for missing required fields', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);

      const invalidData = {
        studentId: 'STU001',
        // Missing amount, paymentType, paymentMethod
      };

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Missing required fields');
    });

    it('should return 400 for invalid amount', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);

      const invalidData = {
        ...validPaymentData,
        amount: -50, // Invalid negative amount
      };

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Amount must be greater than 0');
    });

    it('should return 400 for future payment date', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const invalidData = {
        ...validPaymentData,
        paymentDate: futureDate.toISOString().split('T')[0],
      };

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Payment date cannot be in the future');
    });

    it('should handle database errors', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: true,
        user: mockUser(),
      });
      mockHasPermission.mockReturnValue(true);
      
      mockQuery.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(validPaymentData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create payment');
    });
  });
});
