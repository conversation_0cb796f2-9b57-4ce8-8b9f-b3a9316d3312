/**
 * Cabinet Bookings API endpoint
 * Handles booking operations for specific cabinets
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { BookingStatus } from '@/types';

interface CabinetBooking {
  id: string;
  cabinet_id: string;
  date: Date;
  start_time: string;
  end_time: string;
  booked_by: string;
  purpose?: string;
  status: BookingStatus;
  created_at: Date;
  updated_at: Date;
}

interface BookingWithDetails extends CabinetBooking {
  cabinet_name?: string;
  booked_by_name?: string;
  booked_by_email?: string;
}

// GET /api/cabinets/[id]/bookings - Get bookings for a specific cabinet
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id: cabinetId } = params;

    // Validate UUID format
    if (!isValidUUID(cabinetId)) {
      return createErrorResponse('Invalid cabinet ID format', 400);
    }

    // Check if cabinet exists
    const cabinetCheck = await query(
      'SELECT id, name FROM cabinets WHERE id = $1',
      [cabinetId]
    );

    if (cabinetCheck.rows.length === 0) {
      return createErrorResponse('Cabinet not found', 404);
    }

    const cabinet = cabinetCheck.rows[0];
    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    // Build query conditions
    const conditions: string[] = ['cb.cabinet_id = $1'];
    const queryParams: any[] = [cabinetId];
    let paramIndex = 2;

    // Filter by status
    if (filters.status) {
      conditions.push(`cb.status = $${paramIndex}`);
      queryParams.push(filters.status);
      paramIndex++;
    }

    // Filter by date range
    if (filters.dateFrom) {
      conditions.push(`cb.date >= $${paramIndex}`);
      queryParams.push(filters.dateFrom);
      paramIndex++;
    }

    if (filters.dateTo) {
      conditions.push(`cb.date <= $${paramIndex}`);
      queryParams.push(filters.dateTo);
      paramIndex++;
    }

    // Filter by booked user
    if (filters.bookedBy) {
      conditions.push(`cb.booked_by = $${paramIndex}`);
      queryParams.push(filters.bookedBy);
      paramIndex++;
    }

    // Filter upcoming bookings only
    if (filters.upcoming === 'true') {
      conditions.push(`cb.date >= CURRENT_DATE`);
    }

    // Filter past bookings only
    if (filters.past === 'true') {
      conditions.push(`cb.date < CURRENT_DATE`);
    }

    const whereClause = `WHERE ${conditions.join(' AND ')}`;

    // Build ORDER BY clause
    let orderBy = 'cb.date DESC, cb.start_time DESC';
    if (pagination.sortBy) {
      const sortColumn = pagination.sortBy === 'date' ? 'cb.date' :
                        pagination.sortBy === 'startTime' ? 'cb.start_time' :
                        pagination.sortBy === 'status' ? 'cb.status' :
                        'cb.date';
      orderBy = `${sortColumn} ${pagination.sortOrder}`;
    }

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM cabinet_bookings cb
      ${whereClause}
    `;
    
    const countResult = await query(countSql, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const dataSql = `
      SELECT
        cb.id,
        cb.cabinet_id,
        cb.date,
        cb.start_time,
        cb.end_time,
        cb.booked_by,
        cb.purpose,
        cb.status,
        cb.created_at,
        cb.updated_at,
        c.name as cabinet_name,
        u.name as booked_by_name,
        u.email as booked_by_email
      FROM cabinet_bookings cb
      LEFT JOIN cabinets c ON cb.cabinet_id = c.id
      LEFT JOIN users u ON cb.booked_by = u.id
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(pagination.limit, offset);
    const dataResult = await query<BookingWithDetails>(dataSql, queryParams);

    // Format response
    const bookings = dataResult.rows.map(booking => ({
      id: booking.id,
      cabinetId: booking.cabinet_id,
      date: booking.date,
      startTime: booking.start_time,
      endTime: booking.end_time,
      bookedBy: booking.booked_by,
      purpose: booking.purpose,
      status: booking.status,
      createdAt: booking.created_at,
      updatedAt: booking.updated_at,
      cabinet: {
        id: booking.cabinet_id,
        name: booking.cabinet_name
      },
      bookedByUser: booking.booked_by_name ? {
        id: booking.booked_by,
        name: booking.booked_by_name,
        email: booking.booked_by_email
      } : undefined
    }));

    return createResponse({
      data: bookings,
      cabinet: {
        id: cabinet.id,
        name: cabinet.name
      },
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
        hasNext: pagination.page < Math.ceil(total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    }, true, 'Cabinet bookings retrieved successfully');

  } catch (error) {
    console.error('Error fetching cabinet bookings:', error);
    return createErrorResponse('Failed to fetch cabinet bookings', 500);
  }
}
