import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Innovative Centre - Admin Portal",
  description: "Administrative portal for Innovative Centre Platform - Comprehensive CRM for English tutoring organization",
  keywords: ["admin", "crm", "education", "tutoring", "management"],
  authors: [{ name: "Innovative Centre" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.variable} font-sans antialiased h-full bg-gray-50`}>
        <div id="root" className="h-full">
          {children}
        </div>
      </body>
    </html>
  );
}
