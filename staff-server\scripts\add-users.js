/**
 * <PERSON><PERSON><PERSON> to add test users for the staff system
 * Creates management, reception, and teacher users with working credentials
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

// Test users to create
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'Management123!',
    role: 'management',
    name: 'Management User'
  },
  {
    email: '<EMAIL>',
    password: 'Reception123!',
    role: 'reception',
    name: 'Reception Staff'
  },
  {
    email: '<EMAIL>',
    password: 'Teacher123!',
    role: 'teacher',
    name: 'Teacher User'
  }
];

async function addTestUsers() {
  console.log('👥 Adding test users to staff database...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    console.log('📡 Testing database connection...');
    await pool.query('SELECT NOW()');
    console.log('✅ Connected to staff database');
    
    for (const user of testUsers) {
      console.log(`\n🔐 Creating user: ${user.email}`);
      
      // Check if user already exists
      const existingUser = await pool.query(
        'SELECT id FROM users WHERE email = $1',
        [user.email]
      );
      
      if (existingUser.rows.length > 0) {
        console.log(`⚠️  User ${user.email} already exists, skipping...`);
        continue;
      }
      
      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(user.password, saltRounds);
      
      // Insert user
      const result = await pool.query(
        `INSERT INTO users (email, password_hash, role, name, is_active)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING id, email, role, name`,
        [user.email, passwordHash, user.role, user.name, true]
      );
      
      const newUser = result.rows[0];
      console.log(`✅ Created user: ${newUser.name} (${newUser.email}) - Role: ${newUser.role}`);
      console.log(`   Password: ${user.password}`);
    }
    
    console.log('\n🎉 All test users created successfully!');
    console.log('\n📋 Login credentials:');
    testUsers.forEach(user => {
      console.log(`   ${user.role.toUpperCase()}: ${user.email} / ${user.password}`);
    });
    
  } catch (error) {
    console.error('❌ Error adding test users:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  addTestUsers();
}

module.exports = { addTestUsers };
