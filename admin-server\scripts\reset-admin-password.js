/**
 * <PERSON><PERSON>t to reset admin password
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

async function resetAdminPassword() {
  console.log('🔐 Resetting admin password...');
  
  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', testResult.rows[0].current_time);
    
    // Hash the new password
    const newPassword = 'Admin123!';
    const passwordHash = await bcrypt.hash(newPassword, 12);
    console.log('🔒 Password hashed successfully');
    
    // Update admin password
    const updateResult = await pool.query(
      'UPDATE users SET password_hash = $1 WHERE email = $2 RETURNING id, email, role',
      [passwordHash, '<EMAIL>']
    );
    
    if (updateResult.rows.length > 0) {
      console.log('✅ Admin password updated successfully');
      console.log('👤 Updated user:', updateResult.rows[0]);
      
      // Test the password
      console.log('🧪 Testing password verification...');
      const isValid = await bcrypt.compare(newPassword, passwordHash);
      console.log('🔍 Password verification test:', isValid ? 'PASSED' : 'FAILED');
      
      console.log('\n🔑 Admin Credentials:');
      console.log('Email: <EMAIL>');
      console.log('Password: Admin123!');
      
    } else {
      console.log('❌ Admin user not found');
    }
    
  } catch (error) {
    console.error('❌ Failed to reset password:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Check if DATABASE_URL is provided
if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is required');
  console.log('💡 Make sure you have .env.local file with DATABASE_URL');
  process.exit(1);
}

resetAdminPassword();
