/**
 * Activity Logs Statistics API endpoint
 * Provides statistical information about activity logs
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { ActivityAction, ResourceType } from '@/types';

interface ActivityStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
  byAction: Record<string, number>;
  byResourceType: Record<string, number>;
  byUser: Array<{
    userId: string;
    userName: string;
    userEmail: string;
    count: number;
  }>;
  recentActivity: Array<{
    id: string;
    action: string;
    resourceType: string;
    userName: string;
    timestamp: Date;
    description: string;
  }>;
}

// GET /api/activity-logs/stats - Get activity logs statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view activity log stats)
    if (!hasPermission(authResult.user.role, 'activity-logs', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get total count
      const totalResult = await query(
        'SELECT COUNT(*) as total FROM activity_logs'
      );
      const total = parseInt(totalResult.rows[0].total);

      // Get today's count
      const todayResult = await query(
        'SELECT COUNT(*) as today FROM activity_logs WHERE DATE(timestamp) = CURRENT_DATE'
      );
      const today = parseInt(todayResult.rows[0].today);

      // Get this week's count
      const weekResult = await query(
        'SELECT COUNT(*) as week FROM activity_logs WHERE timestamp >= DATE_TRUNC(\'week\', CURRENT_DATE)'
      );
      const thisWeek = parseInt(weekResult.rows[0].week);

      // Get this month's count
      const monthResult = await query(
        'SELECT COUNT(*) as month FROM activity_logs WHERE timestamp >= DATE_TRUNC(\'month\', CURRENT_DATE)'
      );
      const thisMonth = parseInt(monthResult.rows[0].month);

      // Get count by action
      const actionResult = await query(
        'SELECT action, COUNT(*) as count FROM activity_logs GROUP BY action ORDER BY count DESC'
      );
      const byAction: Record<string, number> = {};
      actionResult.rows.forEach(row => {
        byAction[row.action] = parseInt(row.count);
      });

      // Get count by resource type
      const resourceResult = await query(
        'SELECT resource_type, COUNT(*) as count FROM activity_logs GROUP BY resource_type ORDER BY count DESC'
      );
      const byResourceType: Record<string, number> = {};
      resourceResult.rows.forEach(row => {
        byResourceType[row.resource_type] = parseInt(row.count);
      });

      // Get count by user (top 10)
      const userResult = await query(
        `SELECT 
           al.user_id, u.name as user_name, u.email as user_email, COUNT(*) as count
         FROM activity_logs al
         LEFT JOIN users u ON al.user_id = u.id
         GROUP BY al.user_id, u.name, u.email
         ORDER BY count DESC
         LIMIT 10`
      );
      const byUser = userResult.rows.map(row => ({
        userId: row.user_id,
        userName: row.user_name || 'Unknown',
        userEmail: row.user_email || 'Unknown',
        count: parseInt(row.count)
      }));

      // Get recent activity (last 10)
      const recentResult = await query(
        `SELECT 
           al.id, al.action, al.resource_type, al.timestamp, al.description,
           u.name as user_name
         FROM activity_logs al
         LEFT JOIN users u ON al.user_id = u.id
         ORDER BY al.timestamp DESC
         LIMIT 10`
      );
      const recentActivity = recentResult.rows.map(row => ({
        id: row.id,
        action: row.action,
        resourceType: row.resource_type,
        userName: row.user_name || 'Unknown',
        timestamp: row.timestamp,
        description: row.description || `${row.action} operation on ${row.resource_type}`
      }));

      const stats: ActivityStats = {
        total,
        today,
        thisWeek,
        thisMonth,
        byAction,
        byResourceType,
        byUser,
        recentActivity
      };

      return createResponse(stats, true, 'Activity log statistics retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting activity log stats:', dbError);
      
      // For development, return mock statistics if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockStats: ActivityStats = {
          total: 156,
          today: 12,
          thisWeek: 45,
          thisMonth: 156,
          byAction: {
            'LOGIN': 45,
            'CREATE': 32,
            'UPDATE': 28,
            'DELETE': 15,
            'LOGOUT': 36
          },
          byResourceType: {
            'USER': 89,
            'PAYMENT': 34,
            'CABINET': 23,
            'INVOICE': 10
          },
          byUser: [
            {
              userId: 'mock-admin-id',
              userName: 'System Administrator',
              userEmail: '<EMAIL>',
              count: 89
            },
            {
              userId: 'mock-cashier-id',
              userName: 'Cashier User',
              userEmail: '<EMAIL>',
              count: 67
            }
          ],
          recentActivity: [
            {
              id: 'mock-log-1',
              action: 'LOGIN',
              resourceType: 'USER',
              userName: 'System Administrator',
              timestamp: new Date(),
              description: 'User logged in'
            },
            {
              id: 'mock-log-2',
              action: 'CREATE',
              resourceType: 'PAYMENT',
              userName: 'Cashier User',
              timestamp: new Date(Date.now() - 300000), // 5 minutes ago
              description: 'Recorded payment of $150.00'
            }
          ]
        };

        return createResponse(mockStats, true, 'Activity log statistics retrieved successfully (development mode)');
      }
      
      return createErrorResponse('Activity log statistics service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get activity log stats error:', error);
    return createErrorResponse('Failed to get activity log statistics', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
