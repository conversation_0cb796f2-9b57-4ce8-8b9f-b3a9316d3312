/**
 * User Sessions API endpoint
 * Provides information about active user sessions and login activity
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface SessionInfo {
  userId: string;
  userName: string;
  userEmail: string;
  userRole: string;
  loginTime: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}

interface LoginActivity {
  date: string;
  loginCount: number;
  uniqueUsers: number;
  failedAttempts: number;
}

// GET /api/users/sessions - Get user session information and login activity
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin can view all sessions)
    if (!hasPermission(authResult.user.role, 'users', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build query conditions for activity logs
      const conditions: string[] = ["action = 'LOGIN'"];
      const params: any[] = [];
      let paramIndex = 1;

      // Filter by user
      if (filters.userId) {
        conditions.push(`user_id = $${paramIndex}`);
        params.push(filters.userId);
        paramIndex++;
      }

      // Filter by date range
      if (filters.dateFrom) {
        conditions.push(`DATE(timestamp) >= $${paramIndex}`);
        params.push(filters.dateFrom);
        paramIndex++;
      }

      if (filters.dateTo) {
        conditions.push(`DATE(timestamp) <= $${paramIndex}`);
        params.push(filters.dateTo);
        paramIndex++;
      }

      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      // Get recent login sessions
      const sessionsSql = `
        SELECT DISTINCT ON (al.user_id)
          al.user_id,
          u.name as user_name,
          u.email as user_email,
          u.role as user_role,
          al.timestamp as login_time,
          al.ip_address,
          al.user_agent,
          CASE 
            WHEN al.timestamp >= NOW() - INTERVAL '24 hours' THEN true 
            ELSE false 
          END as is_active
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ${whereClause}
        ORDER BY al.user_id, al.timestamp DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const offset = (pagination.page - 1) * pagination.limit;
      params.push(pagination.limit, offset);

      const sessionsResult = await query<SessionInfo>(sessionsSql, params);

      // Get total count of unique users with login activity
      const countSql = `
        SELECT COUNT(DISTINCT user_id) as total
        FROM activity_logs al
        ${whereClause}
      `;

      const countResult = await query(countSql, params.slice(0, -2)); // Remove limit and offset
      const total = parseInt(countResult.rows[0]?.total || 0);

      // Get daily login activity (last 30 days)
      const activitySql = `
        SELECT 
          DATE(timestamp) as date,
          COUNT(*) as login_count,
          COUNT(DISTINCT user_id) as unique_users,
          COUNT(CASE WHEN details->>'success' = 'false' THEN 1 END) as failed_attempts
        FROM activity_logs 
        WHERE action = 'LOGIN' 
          AND timestamp >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 30
      `;

      const activityResult = await query(activitySql);
      const loginActivity: LoginActivity[] = activityResult.rows.map(row => ({
        date: row.date,
        loginCount: parseInt(row.login_count),
        uniqueUsers: parseInt(row.unique_users),
        failedAttempts: parseInt(row.failed_attempts || 0)
      }));

      // Get current active sessions (last 24 hours)
      const activeSessionsSql = `
        SELECT COUNT(DISTINCT user_id) as active_sessions
        FROM activity_logs 
        WHERE action = 'LOGIN' 
          AND timestamp >= NOW() - INTERVAL '24 hours'
      `;

      const activeSessionsResult = await query(activeSessionsSql);
      const activeSessions = parseInt(activeSessionsResult.rows[0]?.active_sessions || 0);

      // Format sessions response
      const sessions = sessionsResult.rows.map((session: any) => ({
        userId: session.user_id,
        userName: session.user_name,
        userEmail: session.user_email,
        userRole: session.user_role,
        loginTime: session.login_time,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        isActive: session.is_active
      }));

      return createResponse({
        sessions,
        loginActivity,
        summary: {
          activeSessions,
          totalSessions: total,
          last24Hours: sessions.filter(s => s.isActive).length
        },
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Session information retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting sessions:', dbError);
      
      // For development, return mock session data if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockSessions = [
          {
            userId: 'mock-user-1',
            userName: 'Admin User',
            userEmail: '<EMAIL>',
            userRole: 'admin',
            loginTime: new Date(),
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            isActive: true
          },
          {
            userId: 'mock-user-2',
            userName: 'Cashier User',
            userEmail: '<EMAIL>',
            userRole: 'cashier',
            loginTime: new Date(Date.now() - 3600000), // 1 hour ago
            ipAddress: '*************',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            isActive: true
          }
        ];

        const mockActivity = Array.from({ length: 7 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - i);
          return {
            date: date.toISOString().split('T')[0],
            loginCount: Math.floor(Math.random() * 20) + 5,
            uniqueUsers: Math.floor(Math.random() * 10) + 3,
            failedAttempts: Math.floor(Math.random() * 3)
          };
        });

        return createResponse({
          sessions: mockSessions,
          loginActivity: mockActivity,
          summary: {
            activeSessions: 2,
            totalSessions: 2,
            last24Hours: 2
          },
          pagination: {
            page: 1,
            limit: 10,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }, true, 'Session information retrieved successfully (development mode)');
      }
      
      return createErrorResponse('Session service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get sessions error:', error);
    return createErrorResponse('Failed to get session information', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
