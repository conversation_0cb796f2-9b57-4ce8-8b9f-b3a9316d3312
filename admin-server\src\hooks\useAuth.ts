'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
}

interface AuthState {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    isAuthenticated: false,
  });
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = () => {
    try {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');

      if (!token || !userData) {
        setAuthState({
          user: null,
          loading: false,
          isAuthenticated: false,
        });
        return;
      }

      const user = JSON.parse(userData);
      setAuthState({
        user,
        loading: false,
        isAuthenticated: true,
      });
    } catch (error) {
      console.error('Error checking auth:', error);
      logout();
    }
  };

  const login = (user: User, token: string) => {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
    setAuthState({
      user,
      loading: false,
      isAuthenticated: true,
    });
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setAuthState({
      user: null,
      loading: false,
      isAuthenticated: false,
    });
    router.push('/login');
  };

  const hasRole = (requiredRoles: UserRole[]): boolean => {
    if (!authState.user) return false;
    return requiredRoles.includes(authState.user.role);
  };

  const hasPermission = (resource: string, action: string): boolean => {
    if (!authState.user) return false;

    const { role } = authState.user;

    // Define permissions for each role
    const permissions: Record<UserRole, Record<string, string[]>> = {
      [UserRole.ADMIN]: {
        users: ['create', 'read', 'update', 'delete'],
        payments: ['create', 'read', 'update', 'delete'],
        cabinets: ['create', 'read', 'update', 'delete'],
        'activity-logs': ['read', 'export'],
        reports: ['read', 'export'],
        analytics: ['read'],
        profile: ['read', 'update'],
      },
      [UserRole.CASHIER]: {
        payments: ['create', 'read', 'update'],
        reports: ['read'],
        profile: ['read', 'update'],
      },
      [UserRole.ACCOUNTANT]: {
        payments: ['read'],
        reports: ['read', 'export'],
        'activity-logs': ['read'],
        analytics: ['read'],
        profile: ['read', 'update'],
      },
      // Staff server roles (for completeness)
      [UserRole.MANAGEMENT]: {},
      [UserRole.RECEPTION]: {},
      [UserRole.TEACHER]: {},
    };

    const rolePermissions = permissions[role];
    const resourcePermissions = rolePermissions[resource];

    return resourcePermissions?.includes(action) || false;
  };

  return {
    ...authState,
    login,
    logout,
    hasRole,
    hasPermission,
    checkAuth,
  };
}

// Hook for protecting routes
export function useRequireAuth(requiredRoles?: UserRole[]) {
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!auth.loading) {
      if (!auth.isAuthenticated) {
        router.push('/login');
        return;
      }

      if (requiredRoles && !auth.hasRole(requiredRoles)) {
        router.push('/dashboard'); // Redirect to dashboard if no permission
        return;
      }
    }
  }, [auth.loading, auth.isAuthenticated, auth.user, requiredRoles, router]);

  return auth;
}
