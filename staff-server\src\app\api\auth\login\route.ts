/**
 * Staff authentication login endpoint
 * Handles login for Management, Reception, and Teacher roles
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { verifyPassword, generateTokens } from '@/lib/auth';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Get request context for logging
    const context = getRequestContext(request.headers);

    try {
      // Find user by email
      const userResult = await query<User>(
        'SELECT id, email, password_hash, role, name, is_active FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (userResult.rows.length === 0) {
        // Log failed login attempt
        console.log(`Failed staff login attempt for email: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      const user = userResult.rows[0];

      // Check if user is active
      if (!user.is_active) {
        console.log(`Login attempt for inactive staff user: ${email}`);
        return createErrorResponse('Account is deactivated', 401);
      }

      // Verify password
      const isPasswordValid = await verifyPassword(password, user.password_hash);
      if (!isPasswordValid) {
        console.log(`Invalid password for staff user: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Generate tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        role: user.role
      });

      // Log successful login
      await logAuthEvent('LOGIN', user.id, context);

      // Return success response
      return createResponse({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
          isActive: user.is_active
        },
        token: tokens.token,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      }, true, 'Login successful');

    } catch (dbError) {
      console.error('Database error during staff login:', dbError);
      return createErrorResponse('Authentication failed', 500);
    }

  } catch (error) {
    console.error('Staff login error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
