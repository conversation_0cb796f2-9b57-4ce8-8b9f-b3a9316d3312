/**
 * Database connection test endpoint for Staff Server
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    // Import database utilities
    const { testConnection } = await import('@/lib/db');
    
    // Test the connection
    const isConnected = await testConnection();
    
    if (isConnected) {
      return createResponse({
        status: 'connected',
        message: 'Staff database connection successful',
        timestamp: new Date().toISOString()
      }, true, 'Staff database is connected and working properly');
    } else {
      return createErrorResponse('Staff database connection failed', 500);
    }
  } catch (error) {
    console.error('Staff database test error:', error);
    return createErrorResponse(
      `Staff database connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
