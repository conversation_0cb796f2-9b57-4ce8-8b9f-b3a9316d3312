/**
 * User-related types for the Innovative Centre Platform
 */

import { BaseEntity, UserRole } from './common';

// Base user interface
export interface User extends BaseEntity {
  email: string;
  passwordHash: string;
  role: UserRole;
  name: string;
  isActive: boolean;
}

// User creation request
export interface CreateUserRequest {
  email: string;
  password: string;
  role: UserRole;
  name: string;
  isActive?: boolean;
}

// User update request
export interface UpdateUserRequest {
  email?: string;
  password?: string;
  role?: UserRole;
  name?: string;
  isActive?: boolean;
}

// User response (without password)
export interface UserResponse {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Login request
export interface LoginRequest {
  email: string;
  password: string;
}

// Login response
export interface LoginResponse {
  user: UserResponse;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// JWT payload
export interface JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Refresh token request
export interface RefreshTokenRequest {
  refreshToken: string;
}

// Password change request
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// User filter parameters
export interface UserFilterParams {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  createdFrom?: string;
  createdTo?: string;
}

// User statistics
export interface UserStats {
  total: number;
  active: number;
  inactive: number;
  byRole: Record<UserRole, number>;
  recentLogins: number;
}

// Session information
export interface SessionInfo {
  userId: string;
  email: string;
  role: UserRole;
  name: string;
  loginTime: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Permission interface
export interface Permission {
  resource: string;
  actions: string[];
}

// Role permissions mapping
export const RolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    { resource: 'users', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'payments', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'cabinets', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'activity-logs', actions: ['read', 'export'] },
    { resource: 'reports', actions: ['read', 'export'] },
    { resource: 'kpis', actions: ['read', 'update'] }
  ],
  [UserRole.CASHIER]: [
    { resource: 'payments', actions: ['create', 'read', 'update'] },
    { resource: 'invoices', actions: ['create', 'read', 'update'] },
    { resource: 'reports', actions: ['read'] }
  ],
  [UserRole.ACCOUNTANT]: [
    { resource: 'payments', actions: ['read'] },
    { resource: 'invoices', actions: ['read'] },
    { resource: 'reports', actions: ['read', 'export'] },
    { resource: 'activity-logs', actions: ['read'] }
  ],
  [UserRole.MANAGEMENT]: [
    { resource: 'students', actions: ['read'] },
    { resource: 'groups', actions: ['read'] },
    { resource: 'teachers', actions: ['read'] },
    { resource: 'leads', actions: ['read'] },
    { resource: 'reports', actions: ['read', 'export'] },
    { resource: 'kpis', actions: ['read'] }
  ],
  [UserRole.RECEPTION]: [
    { resource: 'students', actions: ['create', 'read', 'update'] },
    { resource: 'leads', actions: ['create', 'read', 'update'] },
    { resource: 'groups', actions: ['read'] }
  ],
  [UserRole.TEACHER]: [
    { resource: 'students', actions: ['read', 'update'] },
    { resource: 'groups', actions: ['read', 'update'] },
    { resource: 'attendance', actions: ['create', 'read', 'update'] }
  ]
};

// Helper function to check if user has permission
export function hasPermission(
  userRole: UserRole,
  resource: string,
  action: string
): boolean {
  const permissions = RolePermissions[userRole];
  const resourcePermission = permissions.find(p => p.resource === resource);
  return resourcePermission?.actions.includes(action) || false;
}
