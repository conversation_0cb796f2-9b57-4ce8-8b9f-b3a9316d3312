/**
 * Activity logging types for the Innovative Centre Platform
 */

import { BaseEntity, ActivityAction, ResourceType } from './common';

// Re-export types for convenience
export { ActivityAction, ResourceType } from './common';

// Activity log interface
export interface ActivityLog extends BaseEntity {
  userId: string;
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  description?: string;
}

// Activity log creation request
export interface CreateActivityLogRequest {
  userId: string;
  action: ActivityAction;
  resourceType: ResourceType;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  description?: string;
}

// Activity log response with user information
export interface ActivityLogResponse extends ActivityLog {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// Activity log filter parameters
export interface ActivityLogFilterParams {
  userId?: string;
  action?: ActivityAction;
  resourceType?: ResourceType;
  resourceId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  ipAddress?: string;
}

// Activity log statistics
export interface ActivityLogStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
  byAction: Record<ActivityAction, number>;
  byResourceType: Record<ResourceType, number>;
  byUser: Array<{
    userId: string;
    userName: string;
    count: number;
  }>;
  recentActivity: ActivityLogResponse[];
}

// Activity log export options
export interface ActivityLogExportOptions {
  format: 'csv' | 'json' | 'pdf';
  filters?: ActivityLogFilterParams;
  includeUserDetails?: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Activity log summary for dashboard
export interface ActivityLogSummary {
  totalActions: number;
  uniqueUsers: number;
  mostActiveUser: {
    userId: string;
    userName: string;
    actionCount: number;
  };
  mostCommonAction: {
    action: ActivityAction;
    count: number;
  };
  recentActions: ActivityLogResponse[];
}

// Activity context for logging
export interface ActivityContext {
  userId: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
}

// Activity log configuration
export interface ActivityLogConfig {
  enabled: boolean;
  retentionDays: number;
  logSensitiveData: boolean;
  excludeActions?: ActivityAction[];
  excludeResourceTypes?: ResourceType[];
  maxLogSize?: number;
}

// Predefined activity descriptions
export const ActivityDescriptions: Record<string, string> = {
  'CREATE_USER': 'Created a new user account',
  'UPDATE_USER': 'Updated user information',
  'DELETE_USER': 'Deactivated user account',
  'LOGIN': 'User logged into the system',
  'LOGOUT': 'User logged out of the system',
  'CREATE_PAYMENT': 'Recorded a new payment',
  'UPDATE_PAYMENT': 'Updated payment information',
  'CREATE_INVOICE': 'Generated a new invoice',
  'UPDATE_INVOICE': 'Updated invoice details',
  'CREATE_CABINET': 'Added a new cabinet',
  'UPDATE_CABINET': 'Updated cabinet information',
  'CREATE_BOOKING': 'Created a cabinet booking',
  'UPDATE_BOOKING': 'Modified cabinet booking',
  'DELETE_BOOKING': 'Cancelled cabinet booking',
  'EXPORT_REPORT': 'Exported system report',
  'UPDATE_KPI': 'Updated KPI data'
};

// Helper function to generate activity description
export function generateActivityDescription(
  action: ActivityAction,
  resourceType: ResourceType,
  customDescription?: string
): string {
  if (customDescription) {
    return customDescription;
  }
  
  const key = `${action}_${resourceType}`;
  return ActivityDescriptions[key] || `${action} operation on ${resourceType}`;
}

// Helper function to sanitize sensitive data
export function sanitizeLogData(data: Record<string, any>): Record<string, any> {
  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'key'];
  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}
