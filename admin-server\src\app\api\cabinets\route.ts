/**
 * Cabinets API endpoint
 * Handles cabinet management operations (list, create)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, parsePaginationParams, parseFilterParams, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logCabinetOperation, getRequestContext } from '@/lib/activity-logger';
import { ActivityAction } from '@/types';

interface Cabinet {
  id: string;
  name: string;
  capacity: number;
  equipment: string[];
  hourly_rate?: number;
  is_available: boolean;
  created_at: Date;
  updated_at: Date;
}

interface CreateCabinetRequest {
  name: string;
  capacity: number;
  equipment?: string[];
  hourlyRate?: number;
  isAvailable?: boolean;
}

// GET /api/cabinets - List cabinets with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    // Build query conditions
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    // Filter by availability
    if (filters.isAvailable !== undefined) {
      conditions.push(`is_available = $${paramIndex}`);
      params.push(filters.isAvailable === 'true');
      paramIndex++;
    }

    // Filter by capacity range
    if (filters.minCapacity) {
      conditions.push(`capacity >= $${paramIndex}`);
      params.push(parseInt(filters.minCapacity));
      paramIndex++;
    }

    if (filters.maxCapacity) {
      conditions.push(`capacity <= $${paramIndex}`);
      params.push(parseInt(filters.maxCapacity));
      paramIndex++;
    }

    // Search in name
    if (filters.search) {
      conditions.push(`name ILIKE $${paramIndex}`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    // Filter by equipment
    if (filters.equipment) {
      conditions.push(`$${paramIndex} = ANY(equipment)`);
      params.push(filters.equipment);
      paramIndex++;
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    let orderBy = 'name ASC';
    if (pagination.sortBy) {
      const sortColumn = pagination.sortBy === 'capacity' ? 'capacity' :
                        pagination.sortBy === 'hourlyRate' ? 'hourly_rate' :
                        pagination.sortBy === 'isAvailable' ? 'is_available' :
                        'name';
      orderBy = `${sortColumn} ${pagination.sortOrder}`;
    }

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM cabinets
      ${whereClause}
    `;
    
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    const offset = (pagination.page - 1) * pagination.limit;
    const dataSql = `
      SELECT 
        id,
        name,
        capacity,
        equipment,
        hourly_rate,
        is_available,
        created_at,
        updated_at
      FROM cabinets
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(pagination.limit, offset);
    const dataResult = await query<Cabinet>(dataSql, params);

    // Format response
    const cabinets = dataResult.rows.map(cabinet => ({
      id: cabinet.id,
      name: cabinet.name,
      capacity: cabinet.capacity,
      equipment: cabinet.equipment || [],
      hourlyRate: cabinet.hourly_rate,
      isAvailable: cabinet.is_available,
      createdAt: cabinet.created_at,
      updatedAt: cabinet.updated_at
    }));

    return createResponse({
      data: cabinets,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit),
        hasNext: pagination.page < Math.ceil(total / pagination.limit),
        hasPrev: pagination.page > 1
      }
    }, true, 'Cabinets retrieved successfully');

  } catch (error) {
    console.error('Error fetching cabinets:', error);
    return createErrorResponse('Failed to fetch cabinets', 500);
  }
}

// POST /api/cabinets - Create a new cabinet
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'create')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body: CreateCabinetRequest = await request.json();
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['name', 'capacity']);
    
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { name, capacity, equipment = [], hourlyRate, isAvailable = true } = body;

    // Validate capacity
    if (capacity <= 0) {
      return createErrorResponse('Capacity must be greater than 0', 400);
    }

    // Validate hourly rate if provided
    if (hourlyRate !== undefined && hourlyRate < 0) {
      return createErrorResponse('Hourly rate cannot be negative', 400);
    }

    // Check if cabinet name already exists
    const existingCabinet = await query(
      'SELECT id FROM cabinets WHERE name = $1',
      [name]
    );

    if (existingCabinet.rows.length > 0) {
      return createErrorResponse('Cabinet with this name already exists', 400);
    }

    // Create cabinet record
    const sql = `
      INSERT INTO cabinets (
        name, capacity, equipment, hourly_rate, is_available
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING id, name, capacity, equipment, hourly_rate, is_available, created_at, updated_at
    `;

    const params = [
      name,
      capacity,
      equipment,
      hourlyRate || null,
      isAvailable
    ];

    const result = await query<Cabinet>(sql, params);
    const cabinet = result.rows[0];

    // Log the cabinet creation
    const context = getRequestContext(request.headers);
    await logCabinetOperation(
      ActivityAction.CREATE,
      authResult.user.id,
      {
        id: cabinet.id,
        name: cabinet.name,
        capacity: cabinet.capacity,
        equipment: cabinet.equipment,
        hourlyRate: cabinet.hourly_rate,
        isAvailable: cabinet.is_available
      },
      undefined,
      context
    );

    // Format response
    const responseCabinet = {
      id: cabinet.id,
      name: cabinet.name,
      capacity: cabinet.capacity,
      equipment: cabinet.equipment || [],
      hourlyRate: cabinet.hourly_rate,
      isAvailable: cabinet.is_available,
      createdAt: cabinet.created_at,
      updatedAt: cabinet.updated_at
    };

    return createResponse(
      responseCabinet,
      true,
      'Cabinet created successfully',
      undefined,
      201
    );

  } catch (error) {
    console.error('Error creating cabinet:', error);
    return createErrorResponse('Failed to create cabinet', 500);
  }
}
