/**
 * Individual Booking API endpoint
 * Handles operations on specific bookings (GET, PUT, DELETE)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logBookingOperation, getRequestContext } from '@/lib/activity-logger';
import { BookingStatus, ActivityAction } from '@/types';
import { BOOKING_CONFIG } from '@shared/utils/constants';

interface CabinetBooking {
  id: string;
  cabinet_id: string;
  date: Date;
  start_time: string;
  end_time: string;
  booked_by: string;
  purpose?: string;
  status: BookingStatus;
  created_at: Date;
  updated_at: Date;
}

interface BookingWithDetails extends CabinetBooking {
  cabinet_name?: string;
  cabinet_capacity?: number;
  booked_by_name?: string;
  booked_by_email?: string;
}

interface UpdateBookingRequest {
  date?: string;
  startTime?: string;
  endTime?: string;
  purpose?: string;
  status?: BookingStatus;
}

// Helper functions (same as in book/route.ts)
function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

function calculateDurationMinutes(startTime: string, endTime: string): number {
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  return endMinutes - startMinutes;
}

async function checkBookingConflicts(
  cabinetId: string, 
  date: string, 
  startTime: string, 
  endTime: string,
  excludeBookingId?: string
): Promise<boolean> {
  let sql = `
    SELECT id FROM cabinet_bookings 
    WHERE cabinet_id = $1 
      AND date = $2 
      AND status IN ('confirmed', 'pending')
      AND (
        (start_time <= $3 AND end_time > $3) OR
        (start_time < $4 AND end_time >= $4) OR
        (start_time >= $3 AND end_time <= $4)
      )
  `;
  
  const params = [cabinetId, date, startTime, endTime];
  
  if (excludeBookingId) {
    sql += ' AND id != $5';
    params.push(excludeBookingId);
  }
  
  const result = await query(sql, params);
  return result.rows.length > 0;
}

// GET /api/bookings/[id] - Get booking by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid booking ID format', 400);
    }

    // Get booking with details
    const sql = `
      SELECT 
        cb.id,
        cb.cabinet_id,
        cb.date,
        cb.start_time,
        cb.end_time,
        cb.booked_by,
        cb.purpose,
        cb.status,
        cb.created_at,
        cb.updated_at,
        c.name as cabinet_name,
        c.capacity as cabinet_capacity,
        u.name as booked_by_name,
        u.email as booked_by_email
      FROM cabinet_bookings cb
      LEFT JOIN cabinets c ON cb.cabinet_id = c.id
      LEFT JOIN users u ON cb.booked_by = u.id
      WHERE cb.id = $1
    `;

    const result = await query<BookingWithDetails>(sql, [id]);

    if (result.rows.length === 0) {
      return createErrorResponse('Booking not found', 404);
    }

    const booking = result.rows[0];

    // Calculate duration
    const durationMinutes = calculateDurationMinutes(booking.start_time, booking.end_time);
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    const duration = hours > 0 ? 
      (minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`) : 
      `${minutes}m`;

    // Format response
    const responseBooking = {
      id: booking.id,
      cabinetId: booking.cabinet_id,
      date: booking.date,
      startTime: booking.start_time,
      endTime: booking.end_time,
      bookedBy: booking.booked_by,
      purpose: booking.purpose,
      status: booking.status,
      createdAt: booking.created_at,
      updatedAt: booking.updated_at,
      duration,
      cabinet: booking.cabinet_name ? {
        id: booking.cabinet_id,
        name: booking.cabinet_name,
        capacity: booking.cabinet_capacity
      } : undefined,
      bookedByUser: booking.booked_by_name ? {
        id: booking.booked_by,
        name: booking.booked_by_name,
        email: booking.booked_by_email
      } : undefined
    };

    return createResponse(responseBooking, true, 'Booking retrieved successfully');

  } catch (error) {
    console.error('Error fetching booking:', error);
    return createErrorResponse('Failed to fetch booking', 500);
  }
}

// PUT /api/bookings/[id] - Update booking
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'update')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid booking ID format', 400);
    }

    const body: UpdateBookingRequest = await request.json();

    // Get current booking data
    const currentSql = `
      SELECT cb.*, c.is_available as cabinet_available
      FROM cabinet_bookings cb
      LEFT JOIN cabinets c ON cb.cabinet_id = c.id
      WHERE cb.id = $1
    `;

    const currentResult = await query(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Booking not found', 404);
    }

    const currentBooking = currentResult.rows[0];

    // Check if booking can be modified
    if (currentBooking.status === BookingStatus.CANCELLED) {
      return createErrorResponse('Cannot modify cancelled booking', 400);
    }

    // Check cancellation policy for past bookings
    const bookingDateTime = new Date(`${currentBooking.date}T${currentBooking.start_time}`);
    const now = new Date();
    const hoursUntilBooking = (bookingDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntilBooking < BOOKING_CONFIG.CANCELLATION_HOURS && body.status === BookingStatus.CANCELLED) {
      return createErrorResponse(
        `Cannot cancel booking less than ${BOOKING_CONFIG.CANCELLATION_HOURS} hours before start time`,
        400
      );
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateParams: any[] = [];
    let paramIndex = 1;

    // Validate and process date update
    if (body.date !== undefined) {
      const bookingDate = new Date(body.date);
      if (isNaN(bookingDate.getTime())) {
        return createErrorResponse('Invalid date format', 400);
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (bookingDate < today) {
        return createErrorResponse('Cannot set booking date to the past', 400);
      }

      updateFields.push(`date = $${paramIndex}`);
      updateParams.push(body.date);
      paramIndex++;
    }

    // Validate and process time updates
    if (body.startTime !== undefined) {
      if (!isValidTimeFormat(body.startTime)) {
        return createErrorResponse('Invalid start time format. Use HH:MM format', 400);
      }
      updateFields.push(`start_time = $${paramIndex}`);
      updateParams.push(body.startTime);
      paramIndex++;
    }

    if (body.endTime !== undefined) {
      if (!isValidTimeFormat(body.endTime)) {
        return createErrorResponse('Invalid end time format. Use HH:MM format', 400);
      }
      updateFields.push(`end_time = $${paramIndex}`);
      updateParams.push(body.endTime);
      paramIndex++;
    }

    // Validate time logic if both times are being updated
    const newStartTime = body.startTime || currentBooking.start_time;
    const newEndTime = body.endTime || currentBooking.end_time;

    if (newStartTime >= newEndTime) {
      return createErrorResponse('End time must be after start time', 400);
    }

    // Validate duration
    const durationMinutes = calculateDurationMinutes(newStartTime, newEndTime);
    
    if (durationMinutes < BOOKING_CONFIG.MIN_BOOKING_DURATION) {
      return createErrorResponse(
        `Minimum booking duration is ${BOOKING_CONFIG.MIN_BOOKING_DURATION} minutes`,
        400
      );
    }

    if (durationMinutes > BOOKING_CONFIG.MAX_BOOKING_DURATION) {
      return createErrorResponse(
        `Maximum booking duration is ${BOOKING_CONFIG.MAX_BOOKING_DURATION / 60} hours`,
        400
      );
    }

    // Check for conflicts if date or time is being changed
    if (body.date !== undefined || body.startTime !== undefined || body.endTime !== undefined) {
      const newDate = body.date || currentBooking.date.toISOString().split('T')[0];
      const hasConflict = await checkBookingConflicts(
        currentBooking.cabinet_id, 
        newDate, 
        newStartTime, 
        newEndTime,
        id
      );
      
      if (hasConflict) {
        return createErrorResponse(
          'Time slot conflicts with another booking. Please choose a different time.',
          409
        );
      }
    }

    // Process other updates
    if (body.purpose !== undefined) {
      updateFields.push(`purpose = $${paramIndex}`);
      updateParams.push(body.purpose || null);
      paramIndex++;
    }

    if (body.status !== undefined) {
      if (!Object.values(BookingStatus).includes(body.status)) {
        return createErrorResponse('Invalid booking status', 400);
      }
      updateFields.push(`status = $${paramIndex}`);
      updateParams.push(body.status);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', 400);
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE clause parameter
    updateParams.push(id);

    // Execute update
    const updateSql = `
      UPDATE cabinet_bookings 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, cabinet_id, date, start_time, end_time, booked_by, purpose, status, created_at, updated_at
    `;

    const updateResult = await query<CabinetBooking>(updateSql, updateParams);
    const updatedBooking = updateResult.rows[0];

    // Log the booking update
    const context = getRequestContext(request.headers);
    await logBookingOperation(
      ActivityAction.UPDATE,
      authResult.user.id,
      {
        id: updatedBooking.id,
        cabinetId: updatedBooking.cabinet_id,
        date: updatedBooking.date,
        startTime: updatedBooking.start_time,
        endTime: updatedBooking.end_time,
        purpose: updatedBooking.purpose,
        status: updatedBooking.status
      },
      {
        id: currentBooking.id,
        cabinetId: currentBooking.cabinet_id,
        date: currentBooking.date,
        startTime: currentBooking.start_time,
        endTime: currentBooking.end_time,
        purpose: currentBooking.purpose,
        status: currentBooking.status
      },
      context
    );

    // Format response
    const responseBooking = {
      id: updatedBooking.id,
      cabinetId: updatedBooking.cabinet_id,
      date: updatedBooking.date,
      startTime: updatedBooking.start_time,
      endTime: updatedBooking.end_time,
      bookedBy: updatedBooking.booked_by,
      purpose: updatedBooking.purpose,
      status: updatedBooking.status,
      createdAt: updatedBooking.created_at,
      updatedAt: updatedBooking.updated_at
    };

    return createResponse(responseBooking, true, 'Booking updated successfully');

  } catch (error) {
    console.error('Error updating booking:', error);
    return createErrorResponse('Failed to update booking', 500);
  }
}

// DELETE /api/bookings/[id] - Cancel booking
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'delete')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid booking ID format', 400);
    }

    // Get current booking data
    const currentSql = `
      SELECT id, cabinet_id, date, start_time, end_time, booked_by, purpose, status, created_at, updated_at
      FROM cabinet_bookings 
      WHERE id = $1
    `;

    const currentResult = await query<CabinetBooking>(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Booking not found', 404);
    }

    const currentBooking = currentResult.rows[0];

    // Check if booking is already cancelled
    if (currentBooking.status === BookingStatus.CANCELLED) {
      return createErrorResponse('Booking is already cancelled', 400);
    }

    // Check cancellation policy
    const bookingDateTime = new Date(`${currentBooking.date}T${currentBooking.start_time}`);
    const now = new Date();
    const hoursUntilBooking = (bookingDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntilBooking < BOOKING_CONFIG.CANCELLATION_HOURS) {
      return createErrorResponse(
        `Cannot cancel booking less than ${BOOKING_CONFIG.CANCELLATION_HOURS} hours before start time`,
        400
      );
    }

    // Cancel the booking
    const cancelSql = `
      UPDATE cabinet_bookings 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING id, cabinet_id, date, start_time, end_time, booked_by, purpose, status, created_at, updated_at
    `;

    const cancelResult = await query<CabinetBooking>(cancelSql, [BookingStatus.CANCELLED, id]);
    const cancelledBooking = cancelResult.rows[0];

    // Log the booking cancellation
    const context = getRequestContext(request.headers);
    await logBookingOperation(
      ActivityAction.DELETE,
      authResult.user.id,
      {
        id: cancelledBooking.id,
        cabinetId: cancelledBooking.cabinet_id,
        date: cancelledBooking.date,
        startTime: cancelledBooking.start_time,
        endTime: cancelledBooking.end_time,
        purpose: cancelledBooking.purpose,
        status: cancelledBooking.status
      },
      {
        id: currentBooking.id,
        cabinetId: currentBooking.cabinet_id,
        date: currentBooking.date,
        startTime: currentBooking.start_time,
        endTime: currentBooking.end_time,
        purpose: currentBooking.purpose,
        status: currentBooking.status
      },
      context
    );

    return createResponse(
      { id: cancelledBooking.id, status: cancelledBooking.status },
      true,
      'Booking cancelled successfully'
    );

  } catch (error) {
    console.error('Error cancelling booking:', error);
    return createErrorResponse('Failed to cancel booking', 500);
  }
}
