/**
 * Activity logs statistics endpoint
 * Provides analytics and insights about system activity
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - only management can view activity stats
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get activity statistics
      const statsResult = await query(`
        WITH activity_overview AS (
          SELECT 
            COUNT(*) as total_activities,
            COUNT(*) FILTER (WHERE timestamp >= CURRENT_DATE) as today_activities,
            COUNT(*) FILTER (WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days') as week_activities,
            COUNT(*) FILTER (WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days') as month_activities
          FROM activity_logs
        ),
        action_breakdown AS (
          SELECT 
            action,
            COUNT(*) as count
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY action
          ORDER BY count DESC
        ),
        resource_breakdown AS (
          SELECT 
            resource_type,
            COUNT(*) as count
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY resource_type
          ORDER BY count DESC
        ),
        user_activity AS (
          SELECT 
            u.name,
            u.email,
            u.role,
            COUNT(al.*) as activity_count
          FROM users u
          LEFT JOIN activity_logs al ON u.id = al.user_id 
            AND al.timestamp >= CURRENT_DATE - INTERVAL '30 days'
          WHERE u.is_active = true
          GROUP BY u.id, u.name, u.email, u.role
          ORDER BY activity_count DESC
          LIMIT 10
        ),
        hourly_activity AS (
          SELECT 
            EXTRACT(hour FROM timestamp) as hour,
            COUNT(*) as count
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
          GROUP BY EXTRACT(hour FROM timestamp)
          ORDER BY hour
        ),
        daily_activity AS (
          SELECT 
            DATE(timestamp) as date,
            COUNT(*) as count
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
          GROUP BY DATE(timestamp)
          ORDER BY date DESC
        )
        SELECT 
          (SELECT row_to_json(activity_overview) FROM activity_overview) as overview,
          (SELECT json_agg(row_to_json(action_breakdown)) FROM action_breakdown) as actions,
          (SELECT json_agg(row_to_json(resource_breakdown)) FROM resource_breakdown) as resources,
          (SELECT json_agg(row_to_json(user_activity)) FROM user_activity) as users,
          (SELECT json_agg(row_to_json(hourly_activity)) FROM hourly_activity) as hourly,
          (SELECT json_agg(row_to_json(daily_activity)) FROM daily_activity) as daily
      `);

      const stats = statsResult.rows[0];

      return createResponse({
        overview: stats.overview,
        actionBreakdown: stats.actions || [],
        resourceBreakdown: stats.resources || [],
        userActivity: stats.users || [],
        hourlyActivity: stats.hourly || [],
        dailyActivity: stats.daily || []
      }, true, 'Activity statistics retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving activity stats:', dbError);
      return createErrorResponse('Failed to retrieve activity statistics', 500);
    }

  } catch (error) {
    console.error('Activity stats error:', error);
    return createErrorResponse('Failed to retrieve activity statistics', 500);
  }
}
