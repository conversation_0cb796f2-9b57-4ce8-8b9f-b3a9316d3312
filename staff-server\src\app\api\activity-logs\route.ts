/**
 * Activity logs endpoints for staff server
 * Provides access to audit trail and activity monitoring
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams
} from '@/lib/utils';
import { UserRole } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface ActivityLogWithUser {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: string;
  user_agent?: string;
  timestamp: Date;
  description?: string;
  user_name?: string;
  user_email?: string;
  user_role?: string;
}

// GET /api/activity-logs - List activity logs with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - only management can view activity logs
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      // Add user filter
      if (filters.userId) {
        conditions['al.user_id'] = filters.userId;
      }

      // Add action filter
      if (filters.action) {
        conditions['al.action'] = filters.action;
      }

      // Add resource type filter
      if (filters.resourceType) {
        conditions['al.resource_type'] = filters.resourceType;
      }

      // Add resource ID filter
      if (filters.resourceId) {
        conditions['al.resource_id'] = filters.resourceId;
      }

      // Add date range filters
      if (filters.dateFrom) {
        conditions['al.timestamp >='] = filters.dateFrom;
      }

      if (filters.dateTo) {
        conditions['al.timestamp <='] = filters.dateTo;
      }

      // Add search filter
      if (filters.search) {
        // Search in description and user name
        const searchTerm = `%${filters.search}%`;
        const searchResult = await query<ActivityLogWithUser>(
          `SELECT COUNT(*) as total FROM activity_logs al
           LEFT JOIN users u ON al.user_id = u.id
           WHERE (al.description ILIKE $1 OR u.name ILIKE $1)`,
          [searchTerm]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const logsResult = await query<ActivityLogWithUser>(
          `SELECT 
             al.id, al.user_id, al.action, al.resource_type, al.resource_id,
             al.old_values, al.new_values, al.ip_address, al.user_agent,
             al.timestamp, al.description,
             u.name as user_name, u.email as user_email, u.role as user_role
           FROM activity_logs al 
           LEFT JOIN users u ON al.user_id = u.id 
           WHERE (al.description ILIKE $1 OR u.name ILIKE $1)
           ORDER BY al.timestamp DESC 
           LIMIT $2 OFFSET $3`,
          [searchTerm, pagination.limit, offset]
        );

        return createResponse({
          logs: logsResult.rows,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Activity logs retrieved successfully');
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM activity_logs al ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get activity logs with user information
      const offset = (pagination.page - 1) * pagination.limit;
      const logsResult = await query<ActivityLogWithUser>(
        `SELECT 
           al.id, al.user_id, al.action, al.resource_type, al.resource_id,
           al.old_values, al.new_values, al.ip_address, al.user_agent,
           al.timestamp, al.description,
           u.name as user_name, u.email as user_email, u.role as user_role
         FROM activity_logs al 
         LEFT JOIN users u ON al.user_id = u.id 
         ${whereClause} 
         ORDER BY al.timestamp DESC 
         LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
        [...params, pagination.limit, offset]
      );

      return createResponse({
        logs: logsResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Activity logs retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving activity logs:', dbError);
      return createErrorResponse('Failed to retrieve activity logs', 500);
    }

  } catch (error) {
    console.error('Get activity logs error:', error);
    return createErrorResponse('Failed to retrieve activity logs', 500);
  }
}
