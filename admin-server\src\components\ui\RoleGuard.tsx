'use client';

import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@/types';

interface RoleGuardProps {
  children: React.ReactNode;
  roles?: UserRole[];
  resource?: string;
  action?: string;
  fallback?: React.ReactNode;
}

export default function RoleGuard({
  children,
  roles,
  resource,
  action,
  fallback = null,
}: RoleGuardProps) {
  const { user, hasRole, hasPermission } = useAuth();

  if (!user) {
    return fallback;
  }

  // Check role-based access
  if (roles && !hasRole(roles)) {
    return fallback;
  }

  // Check permission-based access
  if (resource && action && !hasPermission(resource, action)) {
    return fallback;
  }

  return <>{children}</>;
}

// Specific role guards for common use cases
export function AdminOnly({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function CashierOrAdmin({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles={[UserRole.ADMIN, UserRole.CASHIER]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AccountantOrAdmin({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles={[UserRole.ADMIN, UserRole.ACCOUNTANT]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

// Unauthorized access component
export function UnauthorizedAccess() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
        <p className="text-gray-600 mb-4">
          You don't have permission to access this resource.
        </p>
        <button
          onClick={() => window.history.back()}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Go Back
        </button>
      </div>
    </div>
  );
}
