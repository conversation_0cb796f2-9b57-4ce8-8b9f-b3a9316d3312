/**
 * Simple database connection test for Staff Server
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');

async function testConnection() {
  console.log('🔍 Testing staff database connection...');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    console.log('📡 Attempting to connect to staff database...');
    const client = await pool.connect();
    console.log('✅ Connected to staff database successfully!');
    
    console.log('🔍 Testing query...');
    const result = await client.query('SELECT NOW() as current_time, version() as version');
    console.log('✅ Query successful!');
    console.log('Current time:', result.rows[0].current_time);
    console.log('Database version:', result.rows[0].version);
    
    // Test if staff tables exist
    console.log('🔍 Checking staff database schema...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('📋 Available tables:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    client.release();
    await pool.end();
    
    console.log('🎉 Staff database connection test completed successfully!');
  } catch (error) {
    console.error('❌ Staff database connection failed:', error.message);
    console.error('Error details:', error);
    process.exit(1);
  }
}

testConnection();
