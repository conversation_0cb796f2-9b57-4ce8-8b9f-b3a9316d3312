-- =====================================================
-- INNOVATIVE CENTRE PLATFORM - STAFF DATABASE SCHEMA
-- =====================================================
-- This schema is for the Staff Server which handles:
-- - Student management and enrollment
-- - Lead management and conversion
-- - Group/class management
-- - Teacher management
-- - Operational reporting
-- - Activity logging for operational changes
-- =====================================================
-- NOTE: This is a placeholder schema for Phase 2 development
-- The admin server will be completed first, then this schema
-- will be fully implemented based on operational requirements
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USER MANAGEMENT (STAFF SERVER)
-- =====================================================

-- Users table for staff server authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('management', 'reception', 'teacher')),
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- STUDENT MANAGEMENT
-- =====================================================

-- Students table (placeholder)
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    date_of_birth DATE,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated', 'dropped')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LEAD MANAGEMENT
-- =====================================================

-- Leads table (placeholder)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    source VARCHAR(100),
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'interested', 'enrolled', 'rejected')),
    assigned_to UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- GROUP/CLASS MANAGEMENT
-- =====================================================

-- Groups table (placeholder)
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    level VARCHAR(100),
    teacher_id UUID REFERENCES users(id),
    max_students INTEGER DEFAULT 15,
    schedule JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ACTIVITY LOGGING (STAFF SERVER)
-- =====================================================

-- Activity logs table for staff operations
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- =====================================================
-- PLACEHOLDER TABLES
-- =====================================================
-- These tables will be fully implemented in Phase 2:
-- - student_groups (many-to-many relationship)
-- - attendance records
-- - lesson plans
-- - progress tracking
-- - communication logs
-- - teacher schedules
-- - room assignments
-- - assessment results
-- =====================================================

-- Create basic indexes
CREATE INDEX idx_students_status ON students(status);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_groups_teacher ON groups(teacher_id);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_timestamp ON activity_logs(timestamp);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default management user (password: Management123!)
INSERT INTO users (email, password_hash, role, name) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoO', 'management', 'Management User');

-- =====================================================
-- NOTES FOR PHASE 2 DEVELOPMENT
-- =====================================================
-- When implementing the staff server, consider:
-- 1. Student enrollment workflow
-- 2. Lead conversion tracking
-- 3. Class scheduling and management
-- 4. Teacher performance tracking
-- 5. Student progress monitoring
-- 6. Communication systems
-- 7. Reporting and analytics
-- 8. Integration with admin server for financial data
-- =====================================================
