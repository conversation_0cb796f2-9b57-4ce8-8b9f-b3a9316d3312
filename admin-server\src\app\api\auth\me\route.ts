/**
 * Get Current User API endpoint
 * Returns information about the currently authenticated user
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest } from '@/lib/auth';
import { query } from '@/lib/db';

interface User {
  id: string;
  email: string;
  role: string;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export async function GET(request: NextRequest) {
  try {
    // Get user from request
    const authResult = await getUserFromRequest(request);
    
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    const tokenUser = authResult.user;

    try {
      // Get full user information from database
      const userResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [tokenUser.id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      // Check if user is still active
      if (!user.is_active) {
        return createErrorResponse('Account is deactivated', 401);
      }

      // Return user information (excluding sensitive data)
      return createResponse({
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }, true, 'User information retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting user info:', dbError);
      
      // For development, return token user info if database is not available
      if (process.env.NODE_ENV === 'development') {
        console.log('Using token user info for development');
        return createResponse({
          id: tokenUser.id,
          email: tokenUser.email,
          role: tokenUser.role,
          name: tokenUser.name || 'Development User',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }, true, 'User information retrieved (development mode)');
      }
      
      return createErrorResponse('User service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get user error:', error);
    return createErrorResponse(
      'Failed to get user information',
      500
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
