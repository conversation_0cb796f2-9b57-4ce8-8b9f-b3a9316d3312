/**
 * Database status API endpoint
 * Provides information about database configuration and connection status
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const dbStatus = {
      configured: !!process.env.DATABASE_URL,
      url_format: process.env.DATABASE_URL ? 'Valid PostgreSQL URL' : 'Not configured',
      ssl_mode: process.env.NODE_ENV === 'production' ? 'Required' : 'Optional',
      environment: process.env.NODE_ENV || 'development',
      connection_status: 'Testing...',
      last_test: new Date().toISOString()
    };

    // Basic URL validation
    if (process.env.DATABASE_URL) {
      const url = process.env.DATABASE_URL;
      if (url.startsWith('postgresql://') || url.startsWith('postgres://')) {
        dbStatus.url_format = 'Valid PostgreSQL connection string';
        
        // Extract basic info without exposing credentials
        try {
          const urlObj = new URL(url);
          dbStatus.connection_status = `Configured for ${urlObj.hostname}:${urlObj.port || 5432}`;
        } catch (e) {
          dbStatus.connection_status = 'URL parsing error';
        }
      } else {
        dbStatus.url_format = 'Invalid URL format';
        dbStatus.connection_status = 'Invalid connection string';
      }
    } else {
      dbStatus.connection_status = 'DATABASE_URL not configured';
    }

    return createResponse(
      dbStatus,
      true,
      'Database status retrieved successfully'
    );
  } catch (error) {
    console.error('Database status error:', error);
    return createErrorResponse(
      `Database status check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500
    );
  }
}
