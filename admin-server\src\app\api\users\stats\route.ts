/**
 * User Statistics API endpoint
 * Provides statistical information about users
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface UserStats {
  total: number;
  active: number;
  inactive: number;
  byRole: Record<string, number>;
  recentRegistrations: number;
  loginActivity: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  recentUsers: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
    createdAt: Date;
  }>;
}

// GET /api/users/stats - Get user statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin can view user stats)
    if (!hasPermission(authResult.user.role, 'users', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get total user count
      const totalResult = await query(
        'SELECT COUNT(*) as total FROM users'
      );
      const total = parseInt(totalResult.rows[0].total);

      // Get active user count
      const activeResult = await query(
        'SELECT COUNT(*) as active FROM users WHERE is_active = true'
      );
      const active = parseInt(activeResult.rows[0].active);

      // Get inactive user count
      const inactive = total - active;

      // Get count by role
      const roleResult = await query(
        'SELECT role, COUNT(*) as count FROM users GROUP BY role ORDER BY count DESC'
      );
      const byRole: Record<string, number> = {};
      roleResult.rows.forEach(row => {
        byRole[row.role] = parseInt(row.count);
      });

      // Get recent registrations (last 7 days)
      const recentRegResult = await query(
        'SELECT COUNT(*) as recent FROM users WHERE created_at >= CURRENT_DATE - INTERVAL \'7 days\''
      );
      const recentRegistrations = parseInt(recentRegResult.rows[0].recent);

      // Get login activity from activity logs
      const todayLoginResult = await query(
        `SELECT COUNT(DISTINCT user_id) as today 
         FROM activity_logs 
         WHERE action = 'LOGIN' AND DATE(timestamp) = CURRENT_DATE`
      );
      const todayLogins = parseInt(todayLoginResult.rows[0]?.today || 0);

      const weekLoginResult = await query(
        `SELECT COUNT(DISTINCT user_id) as week 
         FROM activity_logs 
         WHERE action = 'LOGIN' AND timestamp >= DATE_TRUNC('week', CURRENT_DATE)`
      );
      const weekLogins = parseInt(weekLoginResult.rows[0]?.week || 0);

      const monthLoginResult = await query(
        `SELECT COUNT(DISTINCT user_id) as month 
         FROM activity_logs 
         WHERE action = 'LOGIN' AND timestamp >= DATE_TRUNC('month', CURRENT_DATE)`
      );
      const monthLogins = parseInt(monthLoginResult.rows[0]?.month || 0);

      // Get recent users (last 10)
      const recentUsersResult = await query(
        `SELECT id, name, email, role, created_at 
         FROM users 
         ORDER BY created_at DESC 
         LIMIT 10`
      );
      const recentUsers = recentUsersResult.rows.map(row => ({
        id: row.id,
        name: row.name,
        email: row.email,
        role: row.role,
        createdAt: row.created_at
      }));

      const stats: UserStats = {
        total,
        active,
        inactive,
        byRole,
        recentRegistrations,
        loginActivity: {
          today: todayLogins,
          thisWeek: weekLogins,
          thisMonth: monthLogins
        },
        recentUsers
      };

      return createResponse(stats, true, 'User statistics retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting user stats:', dbError);
      
      // For development, return mock statistics if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockStats: UserStats = {
          total: 12,
          active: 10,
          inactive: 2,
          byRole: {
            'admin': 2,
            'cashier': 5,
            'accountant': 5
          },
          recentRegistrations: 3,
          loginActivity: {
            today: 8,
            thisWeek: 12,
            thisMonth: 12
          },
          recentUsers: [
            {
              id: 'mock-user-1',
              name: 'John Doe',
              email: '<EMAIL>',
              role: 'cashier',
              createdAt: new Date()
            },
            {
              id: 'mock-user-2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              role: 'accountant',
              createdAt: new Date(Date.now() - ********) // 1 day ago
            }
          ]
        };

        return createResponse(mockStats, true, 'User statistics retrieved successfully (development mode)');
      }
      
      return createErrorResponse('User statistics service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get user stats error:', error);
    return createErrorResponse('Failed to get user statistics', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
