/**
 * Activity logs cleanup endpoint
 * Removes old activity logs based on retention policy
 */

import { NextRequest } from 'next/server';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { cleanupOldLogs } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

// POST /api/activity-logs/cleanup - Clean up old activity logs
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - only management can trigger cleanup
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Run cleanup
      await cleanupOldLogs();

      return createResponse(
        { message: 'Activity logs cleanup completed successfully' },
        true,
        'Old activity logs have been cleaned up according to retention policy'
      );

    } catch (cleanupError) {
      console.error('Activity logs cleanup error:', cleanupError);
      return createErrorResponse('Failed to cleanup activity logs', 500);
    }

  } catch (error) {
    console.error('Cleanup activity logs error:', error);
    return createErrorResponse('Failed to cleanup activity logs', 500);
  }
}
