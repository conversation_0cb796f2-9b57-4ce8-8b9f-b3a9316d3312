/**
 * User Profile API endpoint
 * Handles user profile operations (view, update own profile)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { getUserFromRequest, hashPassword, verifyPassword } from '@/lib/auth';
import { query } from '@/lib/db';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

interface UpdateProfileRequest {
  name?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
}

// GET /api/users/profile - Get current user's profile
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    try {
      // Get user profile
      const userResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [authResult.user.id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      // Format response (exclude sensitive data)
      const profile = {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };

      return createResponse(profile, true, 'Profile retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting profile:', dbError);
      
      // For development, return mock profile if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockProfile = {
          id: authResult.user.id,
          email: authResult.user.email,
          role: authResult.user.role,
          name: 'Mock User',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockProfile, true, 'Profile retrieved successfully (development mode)');
      }
      
      return createErrorResponse('Profile service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Get profile error:', error);
    return createErrorResponse('Failed to get profile', 500);
  }
}

// PUT /api/users/profile - Update current user's profile
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    const body: UpdateProfileRequest = await request.json();
    const context = getRequestContext(request.headers);

    try {
      // Get current user data
      const currentUserResult = await query<User>(
        'SELECT id, email, password_hash, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [authResult.user.id]
      );

      if (currentUserResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const currentUser = currentUserResult.rows[0];

      // Check if user is active
      if (!currentUser.is_active) {
        return createErrorResponse('Account is deactivated', 401);
      }

      // Build update query dynamically
      const updateFields: string[] = [];
      const updateParams: any[] = [];
      let paramIndex = 1;

      // Update name if provided
      if (body.name !== undefined && body.name.trim() !== currentUser.name) {
        if (body.name.trim().length < 2) {
          return createErrorResponse('Name must be at least 2 characters long', 400);
        }
        updateFields.push(`name = $${paramIndex}`);
        updateParams.push(body.name.trim());
        paramIndex++;
      }

      // Update email if provided
      if (body.email !== undefined && body.email.toLowerCase() !== currentUser.email) {
        // Validate email format
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
          return createErrorResponse('Please enter a valid email address', 400);
        }

        // Check if email is already taken
        const existingUserResult = await query(
          'SELECT id FROM users WHERE email = $1 AND id != $2',
          [body.email.toLowerCase(), currentUser.id]
        );

        if (existingUserResult.rows.length > 0) {
          return createErrorResponse('Email address is already in use', 409);
        }

        updateFields.push(`email = $${paramIndex}`);
        updateParams.push(body.email.toLowerCase());
        paramIndex++;
      }

      // Update password if provided
      if (body.newPassword !== undefined && body.newPassword.trim()) {
        // Validate current password is provided
        if (!body.currentPassword) {
          return createErrorResponse('Current password is required to change password', 400);
        }

        // Verify current password
        const isCurrentPasswordValid = await verifyPassword(body.currentPassword, currentUser.password_hash);
        if (!isCurrentPasswordValid) {
          return createErrorResponse('Current password is incorrect', 400);
        }

        // Validate new password strength
        if (body.newPassword.length < 8) {
          return createErrorResponse('New password must be at least 8 characters long', 400);
        }

        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(body.newPassword)) {
          return createErrorResponse(
            'New password must contain at least one uppercase letter, one lowercase letter, and one number',
            400
          );
        }

        // Check if new password is different from current
        const isSamePassword = await verifyPassword(body.newPassword, currentUser.password_hash);
        if (isSamePassword) {
          return createErrorResponse('New password must be different from current password', 400);
        }

        // Hash new password
        const newPasswordHash = await hashPassword(body.newPassword);
        updateFields.push(`password_hash = $${paramIndex}`);
        updateParams.push(newPasswordHash);
        paramIndex++;
      }

      // If no fields to update
      if (updateFields.length === 0) {
        return createErrorResponse('No changes to update', 400);
      }

      // Add updated_at field
      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

      // Add WHERE clause parameter
      updateParams.push(currentUser.id);

      // Execute update
      const updateSql = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, email, role, name, is_active, created_at, updated_at
      `;

      const updateResult = await query<User>(updateSql, updateParams);
      const updatedUser = updateResult.rows[0];

      // Log the profile update (without sensitive data)
      const logData: any = {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        action: 'profile_update'
      };

      if (body.newPassword) {
        logData.action = 'profile_update_with_password_change';
      }

      await logUserOperation(
        'UPDATE' as any,
        currentUser.id,
        logData,
        {
          id: currentUser.id,
          email: currentUser.email,
          name: currentUser.name
        },
        context
      );

      // Format response
      const responseUser = {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        name: updatedUser.name,
        isActive: updatedUser.is_active,
        createdAt: updatedUser.created_at,
        updatedAt: updatedUser.updated_at
      };

      return createResponse(responseUser, true, 'Profile updated successfully');

    } catch (dbError) {
      console.error('Database error updating profile:', dbError);
      
      // For development, return success if database is not available
      if (process.env.NODE_ENV === 'development') {
        const mockUpdatedUser = {
          id: authResult.user.id,
          email: body.email || authResult.user.email,
          role: authResult.user.role,
          name: body.name || 'Mock User',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        return createResponse(mockUpdatedUser, true, 'Profile updated successfully (development mode)');
      }
      
      return createErrorResponse('Profile update service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Update profile error:', error);
    return createErrorResponse('Failed to update profile', 500);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
